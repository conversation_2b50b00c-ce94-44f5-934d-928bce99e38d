import { defineRouting } from 'next-intl/routing';
import { createNavigation } from 'next-intl/navigation';

export const routing = defineRouting({
  // 支持的语言列表
  locales: ['en', 'zh'],
  
  // 默认语言
  defaultLocale: 'en',
  
  // 语言前缀配置 - 始终显示语言前缀
  localePrefix: 'always',
  
  // 启用语言检测
  localeDetection: true,
  
  // 启用备用链接
  alternateLinks: true,
  
  // 语言 cookie 配置
  localeCookie: {
    name: 'NEXT_LOCALE',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 365 // 1 year
  },
});

// 轻量级的包装器，围绕 Next.js 的导航 API
// 它们将自动处理用户的语言环境
export const { Link, redirect, usePathname, useRouter } = createNavigation(routing);
