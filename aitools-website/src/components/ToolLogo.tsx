import { Tool } from "@/lib/api";
import OptimizedImage, { ImageSizes, ResponsiveSizes } from "./ui/OptimizedImage";


const ToolLogo = ({ tool }: { tool: any }) => {
    if (!tool.logo) {
        return <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">
                {tool.name.charAt(0).toUpperCase()}
            </span>
        </div>
    } else {
        return <OptimizedImage
            src={tool.logo}
            alt={`${tool.name} logo`}
            width={ImageSizes.toolLogo.width}
            height={ImageSizes.toolLogo.height}
            className="rounded-lg object-contain flex-shrink-0"
            sizes={ResponsiveSizes.toolLogo}
            placeholder="blur"
        />
    }
};

export default ToolLogo;