'use client'

import React from 'react';
import { Link } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';
import { Eye, Heart, ExternalLink } from 'lucide-react';
import LikeButton from './tools/LikeButton';
import OptimizedImage, { ImageSizes, ResponsiveSizes } from './ui/OptimizedImage';
import { getToolPricingColor, getToolPricingText } from '@/constants/pricing';
import { useTranslations } from 'next-intl';
import ToolLogo from './ToolLogo';

interface ToolCardProps {
  tool: {
    _id: string;
    name: string;
    description: string;
    website: string;
    logo?: string;
    category: string;
    tags: string[];
    pricing: 'free' | 'freemium' | 'paid';
    views: number;
    likes: number;
  };
  onLoginRequired?: () => void;
  onUnlike?: (toolId: string) => void;
  isInLikedPage?: boolean; // 新增：标识是否在liked页面
  locale?: string;
}

const ToolCardClient = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false, locale = 'en' }: ToolCardProps) => {
  const t = useTranslations('common' );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200" style={{ height: '100%' }}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <ToolLogo tool={tool} />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {tool.name}
              </h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getToolPricingColor(tool.pricing)}`}>
                {getToolPricingText(tool.pricing)}
              </span>
            </div>
          </div>
          
          <a
            href={tool.website}
            target="_blank"
            rel="noopener noreferrer"
            className="text-gray-400 hover:text-blue-600 transition-colors"
          >
            <ExternalLink className="h-5 w-5" />
          </a>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {tool.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {tool.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700"
            >
              {tag}
            </span>
          ))}
          {tool.tags.length > 3 && (
            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700">
              +{tool.tags.length - 3}
            </span>
          )}
        </div>

        {/* Stats and Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Eye className="h-4 w-4" />
              <span>{tool.views}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Heart className="h-4 w-4" />
              <span>{tool.likes}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <LikeButton
              toolId={tool._id}
              initialLikes={tool.likes}
              initialLiked={isInLikedPage} // 在收藏页面，所有工具都应该是已点赞状态
              onLoginRequired={onLoginRequired}
              onUnlike={onUnlike}
              isInLikedPage={isInLikedPage}
            />
            <Link
              href={`/tools/${tool._id}`}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              {t('view_details')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolCardClient;
