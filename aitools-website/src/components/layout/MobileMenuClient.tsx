'use client';

import { useState } from 'react';
import { Link as NextLink } from '@/i18n/routing';
import { FaBars, FaTimes } from 'react-icons/fa';
import SearchFormClient from './SearchFormClient';

interface MobileMenuClientProps {
  links: Array<{ name: string; href: string }>;
  locale: string;
}

const NavLink = ({ children, href, locale }: { children: React.ReactNode; href: string; locale: string }) => (
  <NextLink
    href={href}
    locale={locale}
    className="px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors"
  >
    {children}
  </NextLink>
);

export default function MobileMenuClient({ links, locale }: MobileMenuClientProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <>
      {/* Mobile menu button */}
      <button
        className="md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        aria-label="Open Menu"
      >
        {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
      </button>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden pb-4">
          <nav className="space-y-4">
            {links.map((link) => (
              <NavLink key={link.name} href={link.href} locale={locale}>
                {link.name}
              </NavLink>
            ))}

            {/* Mobile Search */}
            <div className="pt-4">
              <SearchFormClient locale={locale} />
            </div>
          </nav>
        </div>
      )}
    </>
  );
}
