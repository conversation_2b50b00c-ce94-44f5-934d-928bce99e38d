import { Link as NextLink } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';
import { type Locale } from '@/i18n/config';
import UserMenuClient from '../auth/UserMenuClient';
import LanguageSwitcherClient from './LanguageSwitcherClient';
import MobileMenuClient from './MobileMenuClient';
import SearchFormClient from './SearchFormClient';

const NavLink = ({ 
  children, 
  href, 
  locale 
}: { 
  children: React.ReactNode; 
  href: string;
  locale: Locale;
}) => (
  <NextLink
    href={href}
    locale={locale}
    className="px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors"
  >
    {children}
  </NextLink>
);

export default async function Header({ locale }: {locale: Locale}) {
  const t = await getTranslations({ locale, namespace: 'navigation' });

  const links = [
    { name: t('home'), href: '/' },
    { name: t('tools'), href: '/tools' },
    { name: t('categories'), href: '/categories' },
    { name: t('submit'), href: '/submit' },
  ];

  return (
    <header className="bg-white px-4 shadow-sm border-b border-gray-200">
      <div className="flex h-16 items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-8">
          <NextLink 
            href="/" 
            locale={locale}
            className="flex items-center space-x-2 hover:no-underline"
          >
            <img
              src="/logo.png"
              alt="AI Tools Logo"
              className="w-8 h-8 rounded-lg object-cover"
            />
            <span className="text-xl font-bold text-gray-900">
              {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}
            </span>
          </NextLink>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-4">
            {links.map((link) => (
              <NavLink 
                key={link.name} 
                href={link.href}
                locale={locale}
              >
                {link.name}
              </NavLink>
            ))}
          </nav>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-md mx-8 hidden md:block">
          <SearchFormClient locale={locale} />
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-2">
          {/* Language Switcher */}
          <LanguageSwitcherClient currentLocale={locale} />

          {/* User Menu */}
          <UserMenuClient locale={locale} />

          {/* Mobile Menu */}
          <MobileMenuClient links={links} locale={locale} />
        </div>
      </div>
    </header>
  );
}