import React from 'react';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { apiClient, Tool } from '@/lib/api';
import { Locale } from '@/i18n/config';
import AdminToolDetailClient from './AdminToolDetailClient';
import BackButton from './BackButton';
import {
  ArrowLeft,
  ExternalLink,
  Calendar,
  User,
  Tag,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Globe,
  DollarSign
} from 'lucide-react';

// 服务端数据获取函数
async function getToolData(id: string) {
  try {
    const response = await apiClient.getTool(id);

    if (response.success && response.data) {
      return {
        tool: response.data,
        error: null
      };
    } else {
      return {
        tool: null,
        error: response.error || 'Failed to fetch tool'
      };
    }
  } catch (error) {
    console.error('Failed to fetch tool:', error);
    return {
      tool: null,
      error: 'Failed to fetch tool, please try again later'
    };
  }
}

export default async function AdminToolDetailPage({
  params
}: {
  params: Promise<{ locale: Locale; id: string }>
}) {
  const { locale, id } = await params;

  // 验证ID格式
  if (!id || id.length !== 24) {
    notFound();
  }

  // 获取翻译
  const t = await getTranslations({ locale, namespace: 'admin' });
  const tCategories = await getTranslations({ locale, namespace: 'categories' });

  // 获取工具数据
  const { tool, error } = await getToolData(id);

  if (error || !tool) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            {error || t('errors.tool_not_found')}
          </h3>
        </div>
      </div>
    );
  }

  // 格式化日期的辅助函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取状态徽章的辅助函数
  const getStatusBadge = (tool: { status: string; launchDate?: string }) => {
    // Check if published: approved status and launchDate has passed
    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();

    if (isPublished) {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-4 h-4 mr-2" />
          {t('status_labels.published')}
        </span>
      );
    }

    switch (tool.status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-4 h-4 mr-2" />
            {t('status_labels.pending')}
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            <CheckCircle className="w-4 h-4 mr-2" />
            {t('status_labels.approved')}
          </span>
        );

      case 'rejected':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            <XCircle className="w-4 h-4 mr-2" />
            {t('status_labels.rejected')}
          </span>
        );
      default:
        return null;
    }
  };



  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 客户端组件处理交互功能 */}
      <AdminToolDetailClient
        tool={tool}
        locale={locale}
      />

      {/* Back Button */}
      <div className="mb-8">
        <BackButton />

        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-6">
            <img
              src={tool.logo}
              alt={tool.name}
              className="w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"
            />
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{tool.name}</h1>
                {getStatusBadge(tool)}
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {tCategories(`category_names.${tool.category}`) || tool.category}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <DollarSign className="w-3 h-3 mr-1" />
                  {t(`pricing_labels.${tool.pricing}`) || tool.pricing}
                </span>
                <a
                  href={tool.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <Globe className="w-4 h-4 mr-1" />
                  {t('actions.visit_website')}
                  <ExternalLink className="w-3 h-3 ml-1" />
                </a>
              </div>
              <p className="text-gray-600 max-w-3xl">{tool.tagline}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 静态内容 - 服务端渲染 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">

        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Description */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('sections.tool_description')}</h2>
            <div className="prose max-w-none">
              <p className="text-gray-700 leading-relaxed">
                {tool.description}
              </p>
              {tool.longDescription && (
                <div className="mt-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{t('sections.detailed_description')}</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {tool.longDescription}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('sections.tags')}</h2>
            <div className="flex flex-wrap gap-2">
              {tool.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800"
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {/* Screenshots */}
          {tool.screenshots && tool.screenshots.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('sections.screenshot_preview')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {tool.screenshots.map((screenshot, index) => (
                  <img
                    key={index}
                    src={screenshot}
                    alt={`${tool.name} ${t('sections.screenshot_preview')} ${index + 1}`}
                    className="w-full h-48 object-cover rounded-lg border border-gray-200"
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Submission Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('sections.submission_info')}</h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <User className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{tool.submittedBy}</div>
                  <div className="text-sm text-gray-500">{t('fields.submitter_id')}</div>
                </div>
              </div>

              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{formatDate(tool.submittedAt)}</div>
                  <div className="text-sm text-gray-500">{t('fields.submission_time')}</div>
                </div>
              </div>

              {tool.selectedLaunchDate && (
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
                    </div>
                    <div className="text-sm text-gray-500">{t('fields.selected_launch_date')}</div>
                  </div>
                </div>
              )}

              {tool.launchDate && (
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {new Date(tool.launchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
                    </div>
                    <div className="text-sm text-gray-500">{t('fields.actual_launch_date')}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Review Guidelines */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-900 mb-2">{t('sections.review_guidelines')}</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• {t('guidelines.verify_website')}</li>
                  <li>• {t('guidelines.check_description')}</li>
                  <li>• {t('guidelines.confirm_category')}</li>
                  <li>• {t('guidelines.evaluate_quality')}</li>
                  <li>• {t('guidelines.check_duplicates')}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
