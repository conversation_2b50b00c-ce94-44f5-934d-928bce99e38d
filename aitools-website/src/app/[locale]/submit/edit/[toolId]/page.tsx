import { Fragment } from 'react';
import { getCategoryOptions } from '@/constants/categories-i18n';

import SubmitFormClient from '@/components/submit/SubmitFormClient';
import { apiClient, Tool } from '@/lib/api';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';

interface EditToolPageProps {
  params: Promise<{
    locale: string;
    toolId: string;
  }>;
}

export default async function EditToolPage({ params }: EditToolPageProps) {
  const { locale, toolId } = await params;

  // 检查用户认证
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    redirect(`/${locale}/auth/signin?callbackUrl=/${locale}/submit/edit/${toolId}`);
  }

  // 获取工具信息
  // const tool = await getToolInfo(toolId);
  const tool = (await apiClient.getTool(toolId))?.data;

  if (!tool) {
    redirect(`/${locale}/profile/submitted`);
  }

  // 在服务端获取分类选项
  const categoryOptions = await getCategoryOptions(locale);

  return (
    <Fragment>
      <SubmitFormClient
        categoryOptions={categoryOptions}
        isEditMode={true}
        toolId={toolId}
        initialTool={tool}
        // locale={locale}
      />
    </Fragment>
  );
}