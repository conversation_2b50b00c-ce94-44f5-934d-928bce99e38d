import { getTranslations } from 'next-intl/server';
import {
  PRICING_CONFIG,
  formatPrice,
  formatOriginalPrice,
  hasActivePromotion
} from '@/constants/pricing';
import { Tag, CreditCard, CheckCircle } from 'lucide-react';
import { Locale } from '@/i18n/config';

export default async function DemoFeaturesPage({ params }: { params: Promise<{ locale: Locale }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'launch' });

  return (
    <div className="max-w-6xl mx-auto py-8 px-4">
      <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">功能演示页面</h1>
      
      {/* Logo 展示 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-6">1. 网站Logo更新</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-4">Header中的Logo</h3>
            <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-lg">
              <img 
                src="/logo.png" 
                alt="AI Tools Logo" 
                className="w-8 h-8 rounded-lg object-cover"
              />
              <span className="text-xl font-bold text-gray-900">AI工具导航</span>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-4">浏览器Tab图标</h3>
            <p className="text-sm text-gray-600">
              浏览器标签页现在显示自定义logo图标 (logo.png)
            </p>
            <p className="text-xs text-gray-500 mt-2">
              检查浏览器标签页可以看到新的图标
            </p>
          </div>
        </div>
      </div>

      {/* 限时优惠展示 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-6">2. 限时优惠功能</h2>
        
        {/* 优惠配置信息 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-blue-900 mb-2">当前优惠配置</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-600 font-medium">优惠状态:</span>
              <p className="text-blue-800">{hasActivePromotion() ? '启用' : '禁用'}</p>
            </div>
            <div>
              <span className="text-blue-600 font-medium">折扣:</span>
              <p className="text-blue-800">{Math.round((1 - PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice / PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice) * 100)}%</p>
            </div>
            <div>
              <span className="text-blue-600 font-medium">早鸟价:</span>
              <p className="text-blue-800">启用</p>
            </div>
            <div>
              <span className="text-blue-600 font-medium">节省金额:</span>
              <p className="text-blue-800">¥{PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice - PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice}</p>
            </div>
          </div>
        </div>

        {/* 价格对比卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 免费选项 */}
          <div className="border-2 border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <CheckCircle className="h-6 w-6 text-gray-600 mr-3" />
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">免费发布</h4>
                  <p className="text-sm text-gray-600">选择一个月后的任意日期</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                免费
              </div>
            </div>
          </div>

          {/* 付费选项 - 含限时优惠 */}
          <div className="border-2 border-blue-500 bg-blue-50 rounded-lg p-6 relative">
            {/* 推荐标签 */}
            <div className="absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
              推荐
            </div>

            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <CreditCard className="h-6 w-6 text-blue-600 mr-3" />
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">优先发布</h4>
                  <p className="text-sm text-gray-600">立即发布或选择任意日期</p>
                </div>
              </div>

              <div className="text-right">
                {hasActivePromotion() && (
                  <div>
                    {/* 早鸟价标签 */}
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-semibold mb-3 shadow-lg">
                      <Tag className="h-3 w-3 mr-1" />
                      {t('promotion.early_bird')}
                    </div>
                    {/* 价格展示 */}
                    <div className="space-y-1">
                      <div className="text-sm text-gray-500">
                        {t('promotion.original_price')} <span className="line-through font-medium text-gray-400">{formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice)}</span>
                      </div>
                      <div className="text-3xl font-bold text-gray-900">
                        {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)}
                      </div>
                      <div className="inline-flex items-center px-2 py-1 rounded-md bg-green-50 text-green-700 text-xs font-medium">
                        {t('promotion.save_amount')} {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice - PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 支付按钮示例 */}
            <div className="mt-4">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center">
                <CreditCard className="h-4 w-4 mr-2" />
                {hasActivePromotion() ? (
                  <span className="flex items-center">
                    {t('pay_amount', { price: '' }).replace(' ', '')}
                    <span className="line-through text-blue-200 mx-1 text-sm">
                      {formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice)}
                    </span>
                    <span className="font-bold">
                      {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)}
                    </span>
                    <span className="ml-2 px-2 py-0.5 bg-amber-400 text-amber-900 text-xs rounded-full font-medium">
                      {t('promotion.early_bird')}
                    </span>
                  </span>
                ) : (
                  t('pay_amount', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice) })
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 配置说明 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-semibold mb-4">3. 配置说明</h2>
        <div className="space-y-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900">Logo配置:</h4>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Header组件使用 /logo.png 作为网站logo</li>
              <li>Layout文件中配置了favicon图标</li>
              <li>支持中英文两种语言环境</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">限时优惠配置:</h4>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>在 /src/constants/pricing.ts 中统一配置原价和现价</li>
              <li>支持动态控制优惠开关和剩余名额</li>
              <li>价格对比突出显示，吸引用户选择付费方案</li>
              <li>完整的国际化支持</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
