import createMiddleware from 'next-intl/middleware';
import { routing } from './src/i18n/routing';

export default createMiddleware(routing);

export const config = {
  // 匹配所有路径，除了以下路径：
  // - API 路由 (/api)
  // - 静态文件 (_next/static)
  // - 图片文件 (_next/image)
  // - favicon.ico
  // - robots.txt
  // - sitemap.xml
  // - uploads 目录
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)'
  ]
};
