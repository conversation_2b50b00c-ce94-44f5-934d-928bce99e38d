module.exports = {

"[project]/.next-internal/server/app/api/auth/send-code/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts);
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const AccountSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    provider: {
        type: String,
        required: true,
        enum: [
            'google',
            'github',
            'email'
        ]
    },
    providerId: {
        type: String,
        required: true
    },
    providerAccountId: {
        type: String,
        required: true
    },
    accessToken: String,
    refreshToken: String,
    expiresAt: Date
}, {
    _id: false
});
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    name: {
        type: String,
        required: [
            true,
            'Name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Name cannot exceed 100 characters'
        ]
    },
    avatar: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        required: true,
        enum: [
            'user',
            'admin'
        ],
        default: 'user'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    // 个人信息
    bio: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Bio cannot exceed 500 characters'
        ]
    },
    website: {
        type: String,
        trim: true,
        validate: {
            validator: function(v) {
                if (!v) return true; // 允许空值
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Website must be a valid URL starting with http:// or https://'
        }
    },
    location: {
        type: String,
        trim: true,
        maxlength: [
            100,
            'Location cannot exceed 100 characters'
        ]
    },
    // 认证相关
    emailVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: {
        type: String,
        trim: true
    },
    emailVerificationExpires: {
        type: Date
    },
    // OAuth账户关联
    accounts: [
        AccountSchema
    ],
    // 用户行为
    submittedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    likedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    comments: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Comment'
        }
    ],
    // 时间戳
    lastLoginAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
UserSchema.index({
    emailVerificationToken: 1
});
UserSchema.index({
    'accounts.provider': 1,
    'accounts.providerAccountId': 1
});
// 实例方法
UserSchema.methods.addAccount = function(account) {
    // 检查是否已存在相同的账户
    const existingAccount = this.accounts.find((acc)=>acc.provider === account.provider && acc.providerAccountId === account.providerAccountId);
    if (!existingAccount) {
        this.accounts.push(account);
    } else {
        // 更新现有账户信息
        Object.assign(existingAccount, account);
    }
};
UserSchema.methods.removeAccount = function(provider, providerAccountId) {
    this.accounts = this.accounts.filter((acc)=>!(acc.provider === provider && acc.providerAccountId === providerAccountId));
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/dns [external] (dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("dns", () => require("dns"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[project]/src/lib/api-messages.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API 响应消息国际化
__turbopack_context__.s({
    "enMessages": (()=>enMessages),
    "getApiMessage": (()=>getApiMessage),
    "getLocaleFromRequest": (()=>getLocaleFromRequest),
    "zhMessages": (()=>zhMessages)
});
const zhMessages = {
    errors: {
        fetch_failed: '获取数据失败',
        network_error: '网络错误，请重试',
        validation_failed: '验证失败',
        unauthorized: '未授权访问',
        forbidden: '禁止访问',
        not_found: '资源未找到',
        internal_error: '服务器内部错误',
        invalid_request: '无效请求',
        missing_required_field: '缺少必需字段',
        duplicate_name: '名称已存在',
        create_failed: '创建失败',
        update_failed: '更新失败',
        delete_failed: '删除失败'
    },
    success: {
        created: '创建成功',
        updated: '更新成功',
        deleted: '删除成功',
        submitted: '提交成功',
        approved: '批准成功',
        rejected: '拒绝成功',
        published: '发布成功'
    },
    tools: {
        fetch_failed: '获取工具列表失败',
        create_failed: '创建工具失败',
        name_required: 'name 是必需的',
        description_required: 'description 是必需的',
        website_required: 'website 是必需的',
        category_required: 'category 是必需的',
        pricing_required: 'pricing 是必需的',
        submitter_name_required: 'submitterName 是必需的',
        submitter_email_required: 'submitterEmail 是必需的',
        name_exists: '该工具名称已存在',
        website_exists: '该网站已存在',
        submit_success: '工具提交成功，等待审核',
        approve_success: '工具审核通过',
        reject_success: '工具已拒绝',
        approve_failed: '审核通过失败',
        reject_failed: '拒绝失败',
        not_found: '工具未找到',
        update_success: '工具更新成功',
        update_failed: '工具更新失败',
        launch_date_already_set: '此工具已经选择了发布日期',
        free_date_restriction: '免费选项只能选择一个月后的日期',
        paid_date_restriction: '付费选项最早只能选择明天的日期',
        launch_date_set_success: '发布日期设置成功，工具已进入审核队列',
        edit_not_allowed: '当前状态不允许修改发布日期',
        already_published: '工具已发布，无法修改发布日期',
        launch_date_updated: '发布日期修改成功',
        publish_success: '工具发布成功',
        publish_failed: '工具发布失败'
    },
    user: {
        not_found: '用户未找到',
        unauthorized: '用户未授权',
        profile_update_success: '个人资料更新成功',
        profile_update_failed: '个人资料更新失败'
    },
    auth: {
        invalid_credentials: '无效的登录凭据',
        code_sent: '验证码已发送',
        code_send_failed: '验证码发送失败',
        invalid_code: '无效的验证码',
        login_success: '登录成功',
        login_failed: '登录失败',
        logout_success: '退出成功'
    },
    payment: {
        create_intent_failed: '创建支付意图失败',
        payment_success: '支付成功',
        payment_failed: '支付失败',
        webhook_error: 'Webhook 处理错误',
        order_created: '订单创建成功，请完成支付',
        upgrade_order_created: '升级订单创建成功，请完成支付'
    },
    upload: {
        no_file: '请选择要上传的文件',
        invalid_type: '只支持 JPEG、PNG、GIF、WebP 格式的图片',
        file_too_large: '文件大小不能超过 5MB',
        upload_failed: '文件上传失败',
        upload_success: '文件上传成功'
    }
};
const enMessages = {
    errors: {
        fetch_failed: 'Failed to fetch data',
        network_error: 'Network error, please try again',
        validation_failed: 'Validation failed',
        unauthorized: 'Unauthorized access',
        forbidden: 'Access forbidden',
        not_found: 'Resource not found',
        internal_error: 'Internal server error',
        invalid_request: 'Invalid request',
        missing_required_field: 'Missing required field',
        duplicate_name: 'Name already exists',
        create_failed: 'Creation failed',
        update_failed: 'Update failed',
        delete_failed: 'Deletion failed'
    },
    success: {
        created: 'Created successfully',
        updated: 'Updated successfully',
        deleted: 'Deleted successfully',
        submitted: 'Submitted successfully',
        approved: 'Approved successfully',
        rejected: 'Rejected successfully',
        published: 'Published successfully'
    },
    tools: {
        fetch_failed: 'Failed to fetch tools list',
        create_failed: 'Failed to create tool',
        name_required: 'name is required',
        description_required: 'description is required',
        website_required: 'website is required',
        category_required: 'category is required',
        pricing_required: 'pricing is required',
        submitter_name_required: 'submitterName is required',
        submitter_email_required: 'submitterEmail is required',
        name_exists: 'Tool name already exists',
        website_exists: 'Website already exists',
        submit_success: 'Tool submitted successfully, awaiting review',
        approve_success: 'Tool approved successfully',
        reject_success: 'Tool rejected successfully',
        approve_failed: 'Failed to approve tool',
        reject_failed: 'Failed to reject tool',
        not_found: 'Tool not found',
        update_success: 'Tool updated successfully',
        update_failed: 'Failed to update tool',
        launch_date_already_set: 'This tool has already selected a launch date',
        free_date_restriction: 'Free option can only select dates one month later',
        paid_date_restriction: 'Paid option can only select dates from tomorrow',
        launch_date_set_success: 'Launch date set successfully, tool entered review queue',
        edit_not_allowed: 'Current status does not allow modifying launch date',
        already_published: 'Tool already published, cannot modify launch date',
        launch_date_updated: 'Launch date updated successfully',
        publish_success: 'Tool published successfully',
        publish_failed: 'Failed to publish tool'
    },
    user: {
        not_found: 'User not found',
        unauthorized: 'User unauthorized',
        profile_update_success: 'Profile updated successfully',
        profile_update_failed: 'Failed to update profile'
    },
    auth: {
        invalid_credentials: 'Invalid credentials',
        code_sent: 'Verification code sent',
        code_send_failed: 'Failed to send verification code',
        invalid_code: 'Invalid verification code',
        login_success: 'Login successful',
        login_failed: 'Login failed',
        logout_success: 'Logout successful'
    },
    payment: {
        create_intent_failed: 'Failed to create payment intent',
        payment_success: 'Payment successful',
        payment_failed: 'Payment failed',
        webhook_error: 'Webhook processing error',
        order_created: 'Order created successfully, please complete payment',
        upgrade_order_created: 'Upgrade order created successfully, please complete payment'
    },
    upload: {
        no_file: 'Please select a file to upload',
        invalid_type: 'Only JPEG, PNG, GIF, WebP image formats are supported',
        file_too_large: 'File size cannot exceed 5MB',
        upload_failed: 'File upload failed',
        upload_success: 'File uploaded successfully'
    }
};
function getApiMessage(locale, key) {
    const messages = locale === 'zh' ? zhMessages : enMessages;
    // 支持嵌套键，如 'tools.fetch_failed'
    const keys = key.split('.');
    let message = messages;
    for (const k of keys){
        if (message && typeof message === 'object' && k in message) {
            message = message[k];
        } else {
            // 如果找不到对应的键，返回默认的中文消息
            return locale === 'zh' ? '操作失败' : 'Operation failed';
        }
    }
    return typeof message === 'string' ? message : locale === 'zh' ? '操作失败' : 'Operation failed';
}
function getLocaleFromRequest(request) {
    // 首先检查自定义的 X-Locale 头
    const xLocale = request.headers.get('x-locale');
    if (xLocale === 'en' || xLocale === 'zh') {
        return xLocale;
    }
    const acceptLanguage = request.headers.get('accept-language') || '';
    const pathname = new URL(request.url).pathname;
    // 然后检查URL路径中的语言前缀
    if (pathname.startsWith('/en/')) {
        return 'en';
    } else if (pathname.startsWith('/zh/')) {
        return 'zh';
    }
    // 最后检查Accept-Language头
    if (acceptLanguage.includes('en')) {
        return 'en';
    }
    // 默认返回中文
    return 'zh';
}
}}),
"[project]/src/app/api/auth/send-code/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nodemailer$2f$lib$2f$nodemailer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/nodemailer/lib/nodemailer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-messages.ts [app-route] (ecmascript)");
;
;
;
;
;
;
// 邮件传输器
const emailTransporter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nodemailer$2f$lib$2f$nodemailer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: false,
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    }
});
async function POST(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { email } = await request.json();
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email || !emailRegex.test(email)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        // 生成6位数验证码
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
        const verificationToken = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(32).toString('hex');
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期
        // 查找或创建用户
        let user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: email.toLowerCase()
        });
        if (!user) {
            // 创建新用户（未验证状态）
            user = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                email: email.toLowerCase(),
                name: email.split('@')[0],
                emailVerified: false,
                emailVerificationToken: verificationToken,
                emailVerificationExpires: expiresAt
            });
        } else {
            // 更新验证码
            user.emailVerificationToken = verificationToken;
            user.emailVerificationExpires = expiresAt;
        }
        await user.save();
        // 发送验证码邮件
        try {
            await emailTransporter.sendMail({
                to: email,
                from: process.env.EMAIL_FROM,
                subject: 'AI Tools Directory - 登录验证码',
                html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #3B82F6; margin: 0;">AI Tools Directory</h1>
            </div>
            
            <div style="background-color: #f8fafc; padding: 30px; border-radius: 8px; text-align: center;">
              <h2 style="color: #1f2937; margin-bottom: 20px;">您的登录验证码</h2>
              
              <div style="background-color: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <span style="font-size: 32px; font-weight: bold; color: #3B82F6; letter-spacing: 8px;">
                  ${verificationCode}
                </span>
              </div>
              
              <p style="color: #6b7280; margin: 20px 0;">
                请在10分钟内输入此验证码完成登录
              </p>
              
              <p style="color: #ef4444; font-size: 14px; margin-top: 30px;">
                如果您没有请求此验证码，请忽略此邮件
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #9ca3af; font-size: 12px;">
              <p>此邮件由 AI Tools Directory 自动发送，请勿回复</p>
            </div>
          </div>
        `,
                text: `您的 AI Tools Directory 登录验证码是：${verificationCode}。请在10分钟内使用此验证码完成登录。`
            });
            // 为了安全，我们将验证码存储在数据库中而不是直接返回
            // 这里我们临时存储验证码用于验证（实际应用中应该加密存储）
            user.emailVerificationToken = `${verificationToken}:${verificationCode}`;
            await user.save();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'auth.code_sent'),
                token: verificationToken // 返回token用于后续验证
            });
        } catch (emailError) {
            console.error('Email sending error:', emailError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'auth.code_send_failed')
            }, {
                status: 500
            });
        }
    } catch (error) {
        console.error('Send code error:', error);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.internal_error')
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__387c1556._.js.map