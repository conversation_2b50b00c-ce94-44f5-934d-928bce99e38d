{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts) as any;\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAc;IAClD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;AACH,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\nimport { notFound } from 'next/navigation';\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locale || !locales.find(l => l.toString() === locale?.toString())) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,4PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,UAAU,CAAC,uHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,OAAO,QAAQ,aAAa;QACtE,SAAS,uHAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts"], "sourcesContent": ["// 国际化分类配置文件\n// 支持多语言的AI工具分类配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 分类的基础配置（不包含翻译文本）\nexport const CATEGORY_BASE_CONFIGS = [\n  {\n    slug: 'productivity',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'education',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'text-generation',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'audio-processing',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'marketing',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'customer-service',\n    icon: '🎧',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'data-analysis',\n    icon: '📊',\n    color: '#F59E0B'\n  }\n];\n\n// 客户端钩子：获取国际化的分类配置\nexport function useCategoryConfigs(): CategoryConfig[] {\n  const t = useTranslations('categories');\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 客户端钩子：获取分类选项（用于下拉框等）\nexport function useCategoryOptions(): CategoryOption[] {\n  const configs = useCategoryConfigs();\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 客户端钩子：获取包含\"所有分类\"选项的分类选项\nexport function useCategoryOptionsWithAll(): CategoryOption[] {\n  const t = useTranslations('categories');\n  const options = useCategoryOptions();\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 客户端钩子：获取分类名称\nexport function useCategoryName(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 客户端钩子：获取分类描述\nexport function useCategoryDescription(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取国际化的分类配置\nexport async function getCategoryConfigs(locale?: string): Promise<CategoryConfig[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 服务器端函数：获取分类选项\nexport async function getCategoryOptions(locale?: string): Promise<CategoryOption[]> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 服务器端函数：获取包含\"所有分类\"选项的分类选项\nexport async function getCategoryOptionsWithAll(locale?: string): Promise<CategoryOption[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  const options = await getCategoryOptions(locale);\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 服务器端函数：获取分类名称\nexport async function getCategoryName(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 服务器端函数：获取分类描述\nexport async function getCategoryDescription(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取分类配置\nexport async function getCategoryConfig(slug: string, locale?: string): Promise<CategoryConfig | undefined> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.find(config => config.slug === slug);\n}\n\n// 验证分类是否存在的辅助函数\nexport function isValidCategory(slug: string): boolean {\n  return CATEGORY_BASE_CONFIGS.some(config => config.slug === slug);\n}\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map(config => config.slug);\n\n// 分类元数据映射（slug -> 基础配置）\nexport const CATEGORY_BASE_METADATA: Record<string, typeof CATEGORY_BASE_CONFIGS[0]> = \n  CATEGORY_BASE_CONFIGS.reduce((acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  }, {} as Record<string, typeof CATEGORY_BASE_CONFIGS[0]>);\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;;;;;;AAEjB;AACA;;;AAgBO,MAAM,wBAAwB;IACnC;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,UAAU;IAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU;IAEhB,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,SAAS,gBAAgB,IAAY;IAC1C,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,SAAS,uBAAuB,IAAY;IACjD,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAElF,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,eAAe,0BAA0B,MAAe;IAC7D,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAClF,MAAM,UAAU,MAAM,mBAAmB;IAEzC,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,eAAe,gBAAgB,IAAY,EAAE,MAAe;IACjE,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAClF,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,eAAe,uBAAuB,IAAY,EAAE,MAAe;IACxE,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAClF,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,kBAAkB,IAAY,EAAE,MAAe;IACnE,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAGO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,MAAM,iBAAiB,sBAAsB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;AAGtE,MAAM,yBACX,sBAAsB,MAAM,CAAC,CAAC,KAAK;IACjC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { CATEGORY_SLUGS } from '@/constants/categories-i18n';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  launchDate?: Date; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    maxlength: [100, 'Tool name cannot exceed 100 characters']\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Tagline cannot exceed 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [2000, 'Long description cannot exceed 2000 characters']\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: CATEGORY_SLUGS\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Pricing details cannot exceed 500 characters']\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  launchDate: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ launchDate: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAuC;IAC1D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAiD;IACrE;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM,wIAAA,CAAA,iBAAc;IACtB;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAA+C;IAClE;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;SAAW;QAClD,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,YAAY,CAAC;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts"], "sourcesContent": ["// API 响应消息国际化\nexport interface ApiMessages {\n  // 通用错误消息\n  errors: {\n    fetch_failed: string;\n    network_error: string;\n    validation_failed: string;\n    unauthorized: string;\n    forbidden: string;\n    not_found: string;\n    internal_error: string;\n    invalid_request: string;\n    missing_required_field: string;\n    duplicate_name: string;\n    create_failed: string;\n    update_failed: string;\n    delete_failed: string;\n  };\n  \n  // 成功消息\n  success: {\n    created: string;\n    updated: string;\n    deleted: string;\n    submitted: string;\n    approved: string;\n    rejected: string;\n    published: string;\n  };\n  \n  // 工具相关消息\n  tools: {\n    fetch_failed: string;\n    create_failed: string;\n    name_required: string;\n    description_required: string;\n    website_required: string;\n    category_required: string;\n    pricing_required: string;\n    submitter_name_required: string;\n    submitter_email_required: string;\n    name_exists: string;\n    website_exists: string;\n    submit_success: string;\n    approve_success: string;\n    reject_success: string;\n    approve_failed: string;\n    reject_failed: string;\n    not_found: string;\n    update_success: string;\n    update_failed: string;\n    launch_date_already_set: string;\n    free_date_restriction: string;\n    paid_date_restriction: string;\n    launch_date_set_success: string;\n    edit_not_allowed: string;\n    already_published: string;\n    launch_date_updated: string;\n    publish_success: string;\n    publish_failed: string;\n  };\n  \n  // 用户相关消息\n  user: {\n    not_found: string;\n    unauthorized: string;\n    profile_update_success: string;\n    profile_update_failed: string;\n  };\n  \n  // 认证相关消息\n  auth: {\n    invalid_credentials: string;\n    code_sent: string;\n    code_send_failed: string;\n    invalid_code: string;\n    login_success: string;\n    login_failed: string;\n    logout_success: string;\n  };\n  \n  // 支付相关消息\n  payment: {\n    create_intent_failed: string;\n    payment_success: string;\n    payment_failed: string;\n    webhook_error: string;\n    order_created: string;\n    upgrade_order_created: string;\n  };\n\n  // 上传相关消息\n  upload: {\n    no_file: string;\n    invalid_type: string;\n    file_too_large: string;\n    upload_failed: string;\n    upload_success: string;\n  };\n}\n\n// 中文消息\nexport const zhMessages: ApiMessages = {\n  errors: {\n    fetch_failed: '获取数据失败',\n    network_error: '网络错误，请重试',\n    validation_failed: '验证失败',\n    unauthorized: '未授权访问',\n    forbidden: '禁止访问',\n    not_found: '资源未找到',\n    internal_error: '服务器内部错误',\n    invalid_request: '无效请求',\n    missing_required_field: '缺少必需字段',\n    duplicate_name: '名称已存在',\n    create_failed: '创建失败',\n    update_failed: '更新失败',\n    delete_failed: '删除失败',\n  },\n  success: {\n    created: '创建成功',\n    updated: '更新成功',\n    deleted: '删除成功',\n    submitted: '提交成功',\n    approved: '批准成功',\n    rejected: '拒绝成功',\n    published: '发布成功',\n  },\n  tools: {\n    fetch_failed: '获取工具列表失败',\n    create_failed: '创建工具失败',\n    name_required: 'name 是必需的',\n    description_required: 'description 是必需的',\n    website_required: 'website 是必需的',\n    category_required: 'category 是必需的',\n    pricing_required: 'pricing 是必需的',\n    submitter_name_required: 'submitterName 是必需的',\n    submitter_email_required: 'submitterEmail 是必需的',\n    name_exists: '该工具名称已存在',\n    website_exists: '该网站已存在',\n    submit_success: '工具提交成功，等待审核',\n    approve_success: '工具审核通过',\n    reject_success: '工具已拒绝',\n    approve_failed: '审核通过失败',\n    reject_failed: '拒绝失败',\n    not_found: '工具未找到',\n    update_success: '工具更新成功',\n    update_failed: '工具更新失败',\n    launch_date_already_set: '此工具已经选择了发布日期',\n    free_date_restriction: '免费选项只能选择一个月后的日期',\n    paid_date_restriction: '付费选项最早只能选择明天的日期',\n    launch_date_set_success: '发布日期设置成功，工具已进入审核队列',\n    edit_not_allowed: '当前状态不允许修改发布日期',\n    already_published: '工具已发布，无法修改发布日期',\n    launch_date_updated: '发布日期修改成功',\n    publish_success: '工具发布成功',\n    publish_failed: '工具发布失败',\n  },\n  user: {\n    not_found: '用户未找到',\n    unauthorized: '用户未授权',\n    profile_update_success: '个人资料更新成功',\n    profile_update_failed: '个人资料更新失败',\n  },\n  auth: {\n    invalid_credentials: '无效的登录凭据',\n    code_sent: '验证码已发送',\n    code_send_failed: '验证码发送失败',\n    invalid_code: '无效的验证码',\n    login_success: '登录成功',\n    login_failed: '登录失败',\n    logout_success: '退出成功',\n  },\n  payment: {\n    create_intent_failed: '创建支付意图失败',\n    payment_success: '支付成功',\n    payment_failed: '支付失败',\n    webhook_error: 'Webhook 处理错误',\n    order_created: '订单创建成功，请完成支付',\n    upgrade_order_created: '升级订单创建成功，请完成支付',\n  },\n  upload: {\n    no_file: '请选择要上传的文件',\n    invalid_type: '只支持 JPEG、PNG、GIF、WebP 格式的图片',\n    file_too_large: '文件大小不能超过 5MB',\n    upload_failed: '文件上传失败',\n    upload_success: '文件上传成功',\n  },\n};\n\n// 英文消息\nexport const enMessages: ApiMessages = {\n  errors: {\n    fetch_failed: 'Failed to fetch data',\n    network_error: 'Network error, please try again',\n    validation_failed: 'Validation failed',\n    unauthorized: 'Unauthorized access',\n    forbidden: 'Access forbidden',\n    not_found: 'Resource not found',\n    internal_error: 'Internal server error',\n    invalid_request: 'Invalid request',\n    missing_required_field: 'Missing required field',\n    duplicate_name: 'Name already exists',\n    create_failed: 'Creation failed',\n    update_failed: 'Update failed',\n    delete_failed: 'Deletion failed',\n  },\n  success: {\n    created: 'Created successfully',\n    updated: 'Updated successfully',\n    deleted: 'Deleted successfully',\n    submitted: 'Submitted successfully',\n    approved: 'Approved successfully',\n    rejected: 'Rejected successfully',\n    published: 'Published successfully',\n  },\n  tools: {\n    fetch_failed: 'Failed to fetch tools list',\n    create_failed: 'Failed to create tool',\n    name_required: 'name is required',\n    description_required: 'description is required',\n    website_required: 'website is required',\n    category_required: 'category is required',\n    pricing_required: 'pricing is required',\n    submitter_name_required: 'submitterName is required',\n    submitter_email_required: 'submitterEmail is required',\n    name_exists: 'Tool name already exists',\n    website_exists: 'Website already exists',\n    submit_success: 'Tool submitted successfully, awaiting review',\n    approve_success: 'Tool approved successfully',\n    reject_success: 'Tool rejected successfully',\n    approve_failed: 'Failed to approve tool',\n    reject_failed: 'Failed to reject tool',\n    not_found: 'Tool not found',\n    update_success: 'Tool updated successfully',\n    update_failed: 'Failed to update tool',\n    launch_date_already_set: 'This tool has already selected a launch date',\n    free_date_restriction: 'Free option can only select dates one month later',\n    paid_date_restriction: 'Paid option can only select dates from tomorrow',\n    launch_date_set_success: 'Launch date set successfully, tool entered review queue',\n    edit_not_allowed: 'Current status does not allow modifying launch date',\n    already_published: 'Tool already published, cannot modify launch date',\n    launch_date_updated: 'Launch date updated successfully',\n    publish_success: 'Tool published successfully',\n    publish_failed: 'Failed to publish tool',\n  },\n  user: {\n    not_found: 'User not found',\n    unauthorized: 'User unauthorized',\n    profile_update_success: 'Profile updated successfully',\n    profile_update_failed: 'Failed to update profile',\n  },\n  auth: {\n    invalid_credentials: 'Invalid credentials',\n    code_sent: 'Verification code sent',\n    code_send_failed: 'Failed to send verification code',\n    invalid_code: 'Invalid verification code',\n    login_success: 'Login successful',\n    login_failed: 'Login failed',\n    logout_success: 'Logout successful',\n  },\n  payment: {\n    create_intent_failed: 'Failed to create payment intent',\n    payment_success: 'Payment successful',\n    payment_failed: 'Payment failed',\n    webhook_error: 'Webhook processing error',\n    order_created: 'Order created successfully, please complete payment',\n    upgrade_order_created: 'Upgrade order created successfully, please complete payment',\n  },\n  upload: {\n    no_file: 'Please select a file to upload',\n    invalid_type: 'Only JPEG, PNG, GIF, WebP image formats are supported',\n    file_too_large: 'File size cannot exceed 5MB',\n    upload_failed: 'File upload failed',\n    upload_success: 'File uploaded successfully',\n  },\n};\n\n// 获取API消息的工具函数\nexport function getApiMessage(locale: 'zh' | 'en', key: string): string {\n  const messages = locale === 'zh' ? zhMessages : enMessages;\n  \n  // 支持嵌套键，如 'tools.fetch_failed'\n  const keys = key.split('.');\n  let message: any = messages;\n  \n  for (const k of keys) {\n    if (message && typeof message === 'object' && k in message) {\n      message = message[k];\n    } else {\n      // 如果找不到对应的键，返回默认的中文消息\n      return locale === 'zh' ? '操作失败' : 'Operation failed';\n    }\n  }\n  \n  return typeof message === 'string' ? message : (locale === 'zh' ? '操作失败' : 'Operation failed');\n}\n\n// 从请求头中获取语言偏好\nexport function getLocaleFromRequest(request: Request): 'zh' | 'en' {\n  // 首先检查自定义的 X-Locale 头\n  const xLocale = request.headers.get('x-locale');\n  if (xLocale === 'en' || xLocale === 'zh') {\n    return xLocale;\n  }\n\n  const acceptLanguage = request.headers.get('accept-language') || '';\n  const pathname = new URL(request.url).pathname;\n\n  // 然后检查URL路径中的语言前缀\n  if (pathname.startsWith('/en/')) {\n    return 'en';\n  } else if (pathname.startsWith('/zh/')) {\n    return 'zh';\n  }\n\n  // 最后检查Accept-Language头\n  if (acceptLanguage.includes('en')) {\n    return 'en';\n  }\n\n  // 默认返回中文\n  return 'zh';\n}\n"], "names": [], "mappings": "AAAA,cAAc;;;;;;;AAsGP,MAAM,aAA0B;IACrC,QAAQ;QACN,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,wBAAwB;QACxB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,SAAS;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;QACzB,0BAA0B;QAC1B,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,yBAAyB;QACzB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM;QACJ,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,uBAAuB;IACzB;IACA,MAAM;QACJ,qBAAqB;QACrB,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;IACA,SAAS;QACP,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,uBAAuB;IACzB;IACA,QAAQ;QACN,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAGO,MAAM,aAA0B;IACrC,QAAQ;QACN,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,wBAAwB;QACxB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,SAAS;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;QACzB,0BAA0B;QAC1B,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,yBAAyB;QACzB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM;QACJ,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,uBAAuB;IACzB;IACA,MAAM;QACJ,qBAAqB;QACrB,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;IACA,SAAS;QACP,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,uBAAuB;IACzB;IACA,QAAQ;QACN,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAGO,SAAS,cAAc,MAAmB,EAAE,GAAW;IAC5D,MAAM,WAAW,WAAW,OAAO,aAAa;IAEhD,+BAA+B;IAC/B,MAAM,OAAO,IAAI,KAAK,CAAC;IACvB,IAAI,UAAe;IAEnB,KAAK,MAAM,KAAK,KAAM;QACpB,IAAI,WAAW,OAAO,YAAY,YAAY,KAAK,SAAS;YAC1D,UAAU,OAAO,CAAC,EAAE;QACtB,OAAO;YACL,sBAAsB;YACtB,OAAO,WAAW,OAAO,SAAS;QACpC;IACF;IAEA,OAAO,OAAO,YAAY,WAAW,UAAW,WAAW,OAAO,SAAS;AAC7E;AAGO,SAAS,qBAAqB,OAAgB;IACnD,sBAAsB;IACtB,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;IACpC,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,OAAO;IACT;IAEA,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACjE,MAAM,WAAW,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAQ;IAE9C,kBAAkB;IAClB,IAAI,SAAS,UAAU,CAAC,SAAS;QAC/B,OAAO;IACT,OAAO,IAAI,SAAS,UAAU,CAAC,SAAS;QACtC,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,eAAe,QAAQ,CAAC,OAAO;QACjC,OAAO;IACT;IAEA,SAAS;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\nimport { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';\n\n// GET /api/tools - 获取工具列表\nexport async function GET(request: NextRequest) {\n  try {\n    await dbConnect();\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '12');\n    const category = searchParams.get('category');\n    const status = searchParams.get('status');\n    const search = searchParams.get('search');\n    const sort = searchParams.get('sort') || 'createdAt';\n    const order = searchParams.get('order') || 'desc';\n    const dateFrom = searchParams.get('dateFrom');\n    const dateTo = searchParams.get('dateTo');\n\n    // 构建查询条件\n    const query: any = {};\n    \n    if (category && category !== 'all') {\n      query.category = category;\n    }\n    \n    if (status && status !== 'all') {\n      if (status === 'published') {\n        // 查询已发布的工具：approved状态且launchDate已过\n        query.status = 'approved';\n        query.selectedLaunchDate = { $lte: new Date() };\n      } else {\n        query.status = status;\n      }\n    } else {\n      // 默认只显示已发布的工具（对于公开API）\n      query.status = 'approved';\n      query.selectedLaunchDate = { $lte: new Date() };\n    }\n    \n    if (search) {\n      query.$or = [\n        { name: { $regex: search, $options: 'i' } },\n        { description: { $regex: search, $options: 'i' } },\n        { tags: { $in: [new RegExp(search, 'i')] } }\n      ];\n    }\n\n    // 日期筛选\n    if (dateFrom || dateTo) {\n      query.selectedLaunchDate = query.launchDate || {};\n      if (dateFrom) {\n        query.selectedLaunchDate.$gte = new Date(dateFrom);\n      }\n      if (dateTo) {\n        query.selectedLaunchDate.$lte = new Date(dateTo);\n      }\n    }\n\n    // 计算跳过的文档数\n    const skip = (page - 1) * limit;\n\n    // 构建排序条件\n    const sortOrder = order === 'desc' ? -1 : 1;\n    const sortQuery: any = {};\n    sortQuery[sort] = sortOrder;\n\n    // 执行查询\n    const tools = await Tool.find(query)\n      .sort(sortQuery)\n      .skip(skip)\n      .limit(limit)\n      .select('-submittedBy -reviewNotes -reviewedBy')\n      .lean();\n\n    // 获取总数\n    const total = await Tool.countDocuments(query);\n\n    // 计算分页信息\n    const totalPages = Math.ceil(total / limit);\n    const hasNextPage = page < totalPages;\n    const hasPrevPage = page > 1;\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        tools,\n        pagination: {\n          currentPage: page,\n          totalPages,\n          totalItems: total,\n          itemsPerPage: limit,\n          hasNextPage,\n          hasPrevPage\n        }\n      }\n    });\n\n  } catch (error) {\n    console.error('Error fetching tools:', error);\n    const locale = getLocaleFromRequest(request);\n    return NextResponse.json(\n      { success: false, error: getApiMessage(locale, 'tools.fetch_failed') },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/tools - 创建新工具\nexport async function POST(request: NextRequest) {\n  try {\n    await dbConnect();\n\n    const body = await request.json();\n    const locale = getLocaleFromRequest(request);\n\n    // 验证必需字段\n    const requiredFields = ['name', 'description', 'website', 'category', 'pricing', 'submitterName', 'submitterEmail'];\n    for (const field of requiredFields) {\n      if (!body[field]) {\n        return NextResponse.json(\n          { success: false, error: getApiMessage(locale, `tools.${field}_required`) },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 检查工具网站是否已存在\n    const existingTool = await Tool.findOne({ website: body.website });\n    if (existingTool) {\n      return NextResponse.json(\n        { success: false, error: getApiMessage(locale, 'tools.website_exists') },\n        { status: 400 }\n      );\n    }\n\n    // 创建新工具\n    const toolData = {\n      name: body.name,\n      tagline: body.tagline,\n      description: body.description,\n      website: body.website,\n      logo: body.logo,\n      category: body.category,\n      pricing: body.pricing,\n      tags: body.tags || [],\n      submittedBy: body.submitterName, // 临时使用名称，后续应该使用用户ID\n      submittedAt: new Date(),\n      selectedLaunchDate: body.publishDate ? new Date(body.publishDate) : undefined,\n      status: 'draft',\n      views: 0,\n      likes: 0,\n      isActive: true\n    };\n\n    const tool = new Tool(toolData);\n    await tool.save();\n\n    return NextResponse.json({\n      success: true,\n      data: tool,\n      message: getApiMessage(locale, 'tools.submit_success')\n    }, { status: 201 });\n\n  } catch (error: any) {\n    console.error('Error creating tool:', error);\n    const locale = getLocaleFromRequest(request);\n\n    if (error?.name === 'ValidationError') {\n      const validationErrors = Object.values(error.errors).map((err: any) => err.message);\n      return NextResponse.json(\n        { success: false, error: getApiMessage(locale, 'errors.validation_failed'), details: validationErrors },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, error: getApiMessage(locale, 'tools.create_failed') },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;QACzC,MAAM,QAAQ,aAAa,GAAG,CAAC,YAAY;QAC3C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,SAAS;QACT,MAAM,QAAa,CAAC;QAEpB,IAAI,YAAY,aAAa,OAAO;YAClC,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,UAAU,WAAW,OAAO;YAC9B,IAAI,WAAW,aAAa;gBAC1B,mCAAmC;gBACnC,MAAM,MAAM,GAAG;gBACf,MAAM,kBAAkB,GAAG;oBAAE,MAAM,IAAI;gBAAO;YAChD,OAAO;gBACL,MAAM,MAAM,GAAG;YACjB;QACF,OAAO;YACL,uBAAuB;YACvB,MAAM,MAAM,GAAG;YACf,MAAM,kBAAkB,GAAG;gBAAE,MAAM,IAAI;YAAO;QAChD;QAEA,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV;oBAAE,MAAM;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC1C;oBAAE,aAAa;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACjD;oBAAE,MAAM;wBAAE,KAAK;4BAAC,IAAI,OAAO,QAAQ;yBAAK;oBAAC;gBAAE;aAC5C;QACH;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ;YACtB,MAAM,kBAAkB,GAAG,MAAM,UAAU,IAAI,CAAC;YAChD,IAAI,UAAU;gBACZ,MAAM,kBAAkB,CAAC,IAAI,GAAG,IAAI,KAAK;YAC3C;YACA,IAAI,QAAQ;gBACV,MAAM,kBAAkB,CAAC,IAAI,GAAG,IAAI,KAAK;YAC3C;QACF;QAEA,WAAW;QACX,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,SAAS;QACT,MAAM,YAAY,UAAU,SAAS,CAAC,IAAI;QAC1C,MAAM,YAAiB,CAAC;QACxB,SAAS,CAAC,KAAK,GAAG;QAElB,OAAO;QACP,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC,OAC3B,IAAI,CAAC,WACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,MAAM,CAAC,yCACP,IAAI;QAEP,OAAO;QACP,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;QAExC,SAAS;QACT,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QACrC,MAAM,cAAc,OAAO;QAC3B,MAAM,cAAc,OAAO;QAE3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,YAAY;oBACV,aAAa;oBACb;oBACA,YAAY;oBACZ,cAAc;oBACd;oBACA;gBACF;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAsB,GACrE;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QAEpC,SAAS;QACT,MAAM,iBAAiB;YAAC;YAAQ;YAAe;YAAW;YAAY;YAAW;YAAiB;SAAiB;QACnH,KAAK,MAAM,SAAS,eAAgB;YAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;gBAAE,GAC1E;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,cAAc;QACd,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,SAAS,KAAK,OAAO;QAAC;QAChE,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAwB,GACvE;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ;QACR,MAAM,WAAW;YACf,MAAM,KAAK,IAAI;YACf,SAAS,KAAK,OAAO;YACrB,aAAa,KAAK,WAAW;YAC7B,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ;YACvB,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI,IAAI,EAAE;YACrB,aAAa,KAAK,aAAa;YAC/B,aAAa,IAAI;YACjB,oBAAoB,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI;YACpE,QAAQ;YACR,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QAEA,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;QACtB,MAAM,KAAK,IAAI;QAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QACjC,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QAEpC,IAAI,OAAO,SAAS,mBAAmB;YACrC,MAAM,mBAAmB,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAa,IAAI,OAAO;YAClF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAA6B,SAAS;YAAiB,GACtG;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAuB,GACtE;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}