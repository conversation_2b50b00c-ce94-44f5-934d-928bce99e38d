{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // 原价（元）- 用于显示打折前价格\n    originalPrice: 49.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 原价Stripe金额（分为单位）\n    originalStripeAmount: 4990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 限时优惠信息\n    promotion: {\n      // 是否启用限时优惠\n      enabled: true,\n      // 优惠描述\n      description: '限时优惠 - 前100个付费用户',\n      // 折扣百分比\n      discountPercent: 50,\n      // 剩余名额（这个可以从数据库动态获取）\n      remainingSlots: 85\n    },\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化原价显示（带删除线）\nexport const formatOriginalPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 获取促销信息\nexport const getPromotionInfo = () => {\n  return PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n};\n\n// 检查是否有促销活动\nexport const hasActivePromotion = () => {\n  const promotion = PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n  return promotion.enabled && promotion.remainingSlots > 0;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,mBAAmB;QACnB,sBAAsB;QACtB,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,SAAS;QACT,WAAW;YACT,WAAW;YACX,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;QAClB;QACA,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,sBAAsB,CAAC,OAAe;IACjD,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,mBAAmB;IAC9B,OAAO,eAAe,eAAe,CAAC,SAAS;AACjD;AAGO,MAAM,qBAAqB;IAChC,MAAM,YAAY,eAAe,eAAe,CAAC,SAAS;IAC1D,OAAO,UAAU,OAAO,IAAI,UAAU,cAAc,GAAG;AACzD;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts"], "sourcesContent": ["// AI工具应用的预定义标签列表 - 精选最流行的50个标签\n// 标签键值，用于国际化\nexport const AVAILABLE_TAG_KEYS = [\n  // 核心AI功能\n  'ai_assistant', 'chatgpt', 'conversational_ai', 'smart_qa', 'language_model',\n\n  // 内容创作\n  'writing_assistant', 'content_generation', 'copywriting', 'blog_writing', 'marketing_copy',\n\n  // 图像处理\n  'image_generation', 'image_editing', 'ai_painting', 'avatar_generation', 'background_removal',\n\n  // 视频处理\n  'video_generation', 'video_editing', 'video_clipping', 'short_video_creation', 'video_subtitles',\n\n  // 音频处理\n  'speech_synthesis', 'speech_recognition', 'music_generation', 'speech_to_text', 'text_to_speech',\n\n  // 代码开发\n  'code_generation', 'code_completion', 'code_review', 'development_assistant', 'low_code_platform',\n\n  // 数据分析\n  'data_analysis', 'data_visualization', 'business_intelligence', 'machine_learning', 'deep_learning',\n\n  // 办公效率\n  'office_automation', 'document_processing', 'project_management', 'team_collaboration', 'note_taking',\n\n  // 设计工具\n  'ui_design', 'logo_design', 'web_design', 'graphic_design', 'prototype_design',\n\n  // 营销工具\n  'seo_optimization', 'social_media_marketing', 'email_marketing', 'content_marketing', 'market_analysis',\n\n  // 翻译工具\n  'machine_translation', 'real_time_translation', 'document_translation', 'voice_translation'\n];\n\n// 向后兼容的中文标签列表（已弃用，建议使用国际化版本）\nexport const AVAILABLE_TAGS = [\n  // 核心AI功能\n  'AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型',\n\n  // 内容创作\n  '写作助手', '内容生成', '文案创作', '博客写作', '营销文案',\n\n  // 图像处理\n  '图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除',\n\n  // 视频处理\n  '视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕',\n\n  // 音频处理\n  '语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音',\n\n  // 代码开发\n  '代码生成', '代码补全', '代码审查', '开发助手', '低代码平台',\n\n  // 数据分析\n  '数据分析', '数据可视化', '商业智能', '机器学习', '深度学习',\n\n  // 办公效率\n  '办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具',\n\n  // 设计工具\n  'UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计',\n\n  // 营销工具\n  'SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析',\n\n  // 翻译工具\n  '机器翻译', '实时翻译', '文档翻译', '语音翻译'\n];\n\n// 标签的最大选择数量\nexport const MAX_TAGS_COUNT = 3;\n\n// 按分类组织的标签键值（用于国际化）\nexport const TAG_KEYS_BY_CATEGORY = {\n  'core_ai': ['ai_assistant', 'chatgpt', 'conversational_ai', 'smart_qa', 'language_model'],\n  'content_creation': ['writing_assistant', 'content_generation', 'copywriting', 'blog_writing', 'marketing_copy'],\n  'image_processing': ['image_generation', 'image_editing', 'ai_painting', 'avatar_generation', 'background_removal'],\n  'video_processing': ['video_generation', 'video_editing', 'video_clipping', 'short_video_creation', 'video_subtitles'],\n  'audio_processing': ['speech_synthesis', 'speech_recognition', 'music_generation', 'speech_to_text', 'text_to_speech'],\n  'code_development': ['code_generation', 'code_completion', 'code_review', 'development_assistant', 'low_code_platform'],\n  'data_analysis': ['data_analysis', 'data_visualization', 'business_intelligence', 'machine_learning', 'deep_learning'],\n  'office_productivity': ['office_automation', 'document_processing', 'project_management', 'team_collaboration', 'note_taking'],\n  'design_tools': ['ui_design', 'logo_design', 'web_design', 'graphic_design', 'prototype_design'],\n  'marketing_tools': ['seo_optimization', 'social_media_marketing', 'email_marketing', 'content_marketing', 'market_analysis'],\n  'translation_tools': ['machine_translation', 'real_time_translation', 'document_translation', 'voice_translation']\n};\n\n// 向后兼容的中文分类标签（已弃用，建议使用国际化版本）\nexport const TAGS_BY_CATEGORY = {\n  '核心AI功能': ['AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型'],\n  '内容创作': ['写作助手', '内容生成', '文案创作', '博客写作', '营销文案'],\n  '图像处理': ['图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除'],\n  '视频处理': ['视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕'],\n  '音频处理': ['语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音'],\n  '代码开发': ['代码生成', '代码补全', '代码审查', '开发助手', '低代码平台'],\n  '数据分析': ['数据分析', '数据可视化', '商业智能', '机器学习', '深度学习'],\n  '办公效率': ['办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具'],\n  '设计工具': ['UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计'],\n  '营销工具': ['SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析'],\n  '翻译工具': ['机器翻译', '实时翻译', '文档翻译', '语音翻译']\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,aAAa;;;;;;;;AACN,MAAM,qBAAqB;IAChC,SAAS;IACT;IAAgB;IAAW;IAAqB;IAAY;IAE5D,OAAO;IACP;IAAqB;IAAsB;IAAe;IAAgB;IAE1E,OAAO;IACP;IAAoB;IAAiB;IAAe;IAAqB;IAEzE,OAAO;IACP;IAAoB;IAAiB;IAAkB;IAAwB;IAE/E,OAAO;IACP;IAAoB;IAAsB;IAAoB;IAAkB;IAEhF,OAAO;IACP;IAAmB;IAAmB;IAAe;IAAyB;IAE9E,OAAO;IACP;IAAiB;IAAsB;IAAyB;IAAoB;IAEpF,OAAO;IACP;IAAqB;IAAuB;IAAsB;IAAsB;IAExF,OAAO;IACP;IAAa;IAAe;IAAc;IAAkB;IAE5D,OAAO;IACP;IAAoB;IAA0B;IAAmB;IAAqB;IAEtF,OAAO;IACP;IAAuB;IAAyB;IAAwB;CACzE;AAGM,MAAM,iBAAiB;IAC5B,SAAS;IACT;IAAQ;IAAW;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAS;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAS;IAAQ;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAQ;IAAU;IAAQ;IAAQ;IAElC,OAAO;IACP;IAAS;IAAU;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;CACzB;AAGM,MAAM,iBAAiB;AAGvB,MAAM,uBAAuB;IAClC,WAAW;QAAC;QAAgB;QAAW;QAAqB;QAAY;KAAiB;IACzF,oBAAoB;QAAC;QAAqB;QAAsB;QAAe;QAAgB;KAAiB;IAChH,oBAAoB;QAAC;QAAoB;QAAiB;QAAe;QAAqB;KAAqB;IACnH,oBAAoB;QAAC;QAAoB;QAAiB;QAAkB;QAAwB;KAAkB;IACtH,oBAAoB;QAAC;QAAoB;QAAsB;QAAoB;QAAkB;KAAiB;IACtH,oBAAoB;QAAC;QAAmB;QAAmB;QAAe;QAAyB;KAAoB;IACvH,iBAAiB;QAAC;QAAiB;QAAsB;QAAyB;QAAoB;KAAgB;IACtH,uBAAuB;QAAC;QAAqB;QAAuB;QAAsB;QAAsB;KAAc;IAC9H,gBAAgB;QAAC;QAAa;QAAe;QAAc;QAAkB;KAAmB;IAChG,mBAAmB;QAAC;QAAoB;QAA0B;QAAmB;QAAqB;KAAkB;IAC5H,qBAAqB;QAAC;QAAuB;QAAyB;QAAwB;KAAoB;AACpH;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QAAC;QAAQ;QAAW;QAAQ;QAAQ;KAAO;IACrD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IAClD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IACjD,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAS;QAAQ;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAClD,QAAQ;QAAC;QAAS;QAAU;QAAQ;QAAQ;KAAO;IACnD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;KAAO;AAC1C", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts"], "sourcesContent": ["// 国际化标签配置文件\n// 支持多语言的AI工具标签配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\nimport { AVAILABLE_TAG_KEYS, TAG_KEYS_BY_CATEGORY } from './tags';\n\nexport interface TagOption {\n  key: string;\n  label: string;\n}\n\nexport interface TagCategory {\n  key: string;\n  name: string;\n  tags: TagOption[];\n}\n\n// 客户端钩子：获取国际化的标签选项\nexport function useTagOptions(): TagOption[] {\n  const t = useTranslations('tags');\n  \n  return AVAILABLE_TAG_KEYS.map(key => ({\n    key,\n    label: t(key)\n  }));\n}\n\n// 客户端钩子：获取按分类组织的国际化标签\nexport function useTagsByCategory(): TagCategory[] {\n  const t = useTranslations('tags');\n  const categoryT = useTranslations('tag_categories');\n  \n  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({\n    key: categoryKey,\n    name: categoryT(categoryKey),\n    tags: tagKeys.map(tagKey => ({\n      key: tagKey,\n      label: t(tagKey)\n    }))\n  }));\n}\n\n// 客户端钩子：获取单个标签的翻译\nexport function useTagLabel(tagKey: string): string {\n  const t = useTranslations('tags');\n  return t(tagKey) || tagKey;\n}\n\n// 客户端钩子：获取标签分类名称\nexport function useTagCategoryName(categoryKey: string): string {\n  const t = useTranslations('tag_categories');\n  return t(categoryKey) || categoryKey;\n}\n\n// 获取所有可用的标签键值（用于验证）\nexport function getAvailableTagKeys(): string[] {\n  return AVAILABLE_TAG_KEYS;\n}\n\n// 获取所有可用的标签分类键值（用于验证）\nexport function getAvailableTagCategoryKeys(): string[] {\n  return Object.keys(TAG_KEYS_BY_CATEGORY);\n}\n\n// 服务器端函数：获取国际化的标签选项\nexport async function getTagOptions(locale?: string): Promise<TagOption[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n\n  return AVAILABLE_TAG_KEYS.map(key => ({\n    key,\n    label: t(key)\n  }));\n}\n\n// 服务器端函数：获取按分类组织的国际化标签\nexport async function getTagsByCategory(locale?: string): Promise<TagCategory[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n  const categoryT = await getTranslations({ locale: locale || 'en', namespace: 'tag_categories' });\n\n  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({\n    key: categoryKey,\n    name: categoryT(categoryKey),\n    tags: tagKeys.map(tagKey => ({\n      key: tagKey,\n      label: t(tagKey)\n    }))\n  }));\n}\n\n// 服务器端函数：获取单个标签的翻译\nexport async function getTagLabel(tagKey: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n  return t(tagKey) || tagKey;\n}\n\n// 服务器端函数：获取标签分类名称\nexport async function getTagCategoryName(categoryKey: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tag_categories' });\n  return t(categoryKey) || categoryKey;\n}\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;AAEjB;AACA;AACA;;;;AAcO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,wHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YACpC;YACA,OAAO,EAAE;QACX,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAElC,OAAO,OAAO,OAAO,CAAC,wHAAA,CAAA,uBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAK,CAAC;YAC3E,KAAK;YACL,MAAM,UAAU;YAChB,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE;gBACX,CAAC;QACH,CAAC;AACH;AAGO,SAAS,YAAY,MAAc;IACxC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,WAAW;AACtB;AAGO,SAAS,mBAAmB,WAAmB;IACpD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,gBAAgB;AAC3B;AAGO,SAAS;IACd,OAAO,wHAAA,CAAA,qBAAkB;AAC3B;AAGO,SAAS;IACd,OAAO,OAAO,IAAI,CAAC,wHAAA,CAAA,uBAAoB;AACzC;AAGO,eAAe,cAAc,MAAe;IACjD,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAE5E,OAAO,wHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YACpC;YACA,OAAO,EAAE;QACX,CAAC;AACH;AAGO,eAAe,kBAAkB,MAAe;IACrD,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAC5E,MAAM,YAAY,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAiB;IAE9F,OAAO,OAAO,OAAO,CAAC,wHAAA,CAAA,uBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAK,CAAC;YAC3E,KAAK;YACL,MAAM,UAAU;YAChB,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE;gBACX,CAAC;QACH,CAAC;AACH;AAGO,eAAe,YAAY,MAAc,EAAE,MAAe;IAC/D,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAC5E,OAAO,EAAE,WAAW;AACtB;AAGO,eAAe,mBAAmB,WAAmB,EAAE,MAAe;IAC3E,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAiB;IACtF,OAAO,EAAE,gBAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport { Tag, Search, X } from 'lucide-react';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport { useTagOptions } from '@/constants/tags-i18n';\nimport { Locale } from '@/i18n/config';\n\ninterface TagSelectorProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  maxTags?: number;\n  placeholder?: string;\n}\n\nexport default function TagSelector({\n  selectedTags,\n  onTagsChange,\n  maxTags = MAX_TAGS_COUNT,\n  placeholder\n}: TagSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n\n  const pathname = usePathname();\n  const t = useTranslations('common');\n  const tagOptions = useTagOptions();\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const toggleTag = (tagKey: string) => {\n    if (selectedTags.includes(tagKey)) {\n      onTagsChange(selectedTags.filter(t => t !== tagKey));\n    } else if (selectedTags.length < maxTags) {\n      onTagsChange([...selectedTags, tagKey]);\n    }\n  };\n\n  const removeTag = (tagKey: string) => {\n    onTagsChange(selectedTags.filter(t => t !== tagKey));\n  };\n\n  // 过滤标签：根据搜索词过滤，并排除已选择的标签\n  const filteredTags = tagOptions.filter(tag =>\n    tag.label.toLowerCase().includes(searchTerm.toLowerCase()) &&\n    !selectedTags.includes(tag.key)\n  );\n\n  // 获取已选择标签的显示文本\n  const getSelectedTagLabel = (tagKey: string) => {\n    const tagOption = tagOptions.find(tag => tag.key === tagKey);\n    return tagOption ? tagOption.label : tagKey;\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 标题和计数器 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{t('select_tags')}</h3>\n        <span className=\"text-sm text-gray-500\">\n          {t('selected_count', { count: selectedTags.length, max: maxTags })}\n        </span>\n      </div>\n\n      {/* 已选择的标签 */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-700\">{t('selected_tags')}</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tagKey) => (\n              <span\n                key={tagKey}\n                className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n              >\n                {getSelectedTagLabel(tagKey)}\n                <button\n                  type=\"button\"\n                  onClick={() => removeTag(tagKey)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 标签选择器 */}\n      <div className=\"space-y-3\">\n        <div className=\"relative\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('select_tags_max', { max: maxTags })}\n          </label>\n\n          {/* 搜索框 */}\n          <div className=\"relative mb-3\">\n            <input\n              type=\"text\"\n              placeholder={placeholder || t('search_tags')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onFocus={() => setIsOpen(true)}\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n          </div>\n\n          {/* 标签选择下拉框 */}\n          {(isOpen || searchTerm) && (\n            <div className=\"relative\">\n              <div className=\"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                {filteredTags.length > 0 ? (\n                  <div className=\"p-2\">\n                    <div className=\"grid grid-cols-1 gap-1\">\n                      {filteredTags.map((tag) => {\n                        const isDisabled = selectedTags.length >= maxTags;\n\n                        return (\n                          <button\n                            key={tag.key}\n                            type=\"button\"\n                            onClick={() => {\n                              toggleTag(tag.key);\n                              setSearchTerm('');\n                              setIsOpen(false);\n                            }}\n                            disabled={isDisabled}\n                            className={`\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ${isDisabled\n                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                                : 'hover:bg-blue-50 text-gray-700'\n                              }\n                            `}\n                          >\n                            <div className=\"flex items-center\">\n                              <Tag className=\"h-3 w-3 mr-2 text-gray-400\" />\n                              {tag.label}\n                            </div>\n                          </button>\n                        );\n                      })}\n                    </div>\n                    {filteredTags.length > 50 && (\n                      <p className=\"text-xs text-gray-500 mt-2 px-3\">\n                        {t('found_tags', { count: filteredTags.length })}\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"p-4 text-center text-gray-500 text-sm\">\n                    {searchTerm ? t('no_tags_found') : t('start_typing')}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭下拉框 */}\n      {(isOpen || searchTerm) && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => {\n            setIsOpen(false);\n            setSearchTerm('');\n          }}\n        />\n      )}\n\n      {/* 提示信息 */}\n      {selectedTags.length >= maxTags && (\n        <p className=\"text-sm text-amber-600\">\n          {t('max_tags_limit', { max: maxTags })}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AAiBe,SAAS,YAAY,EAClC,YAAY,EACZ,YAAY,EACZ,UAAU,wHAAA,CAAA,iBAAc,EACxB,WAAW,EACM;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE/B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,YAAY,CAAC;QACjB,IAAI,aAAa,QAAQ,CAAC,SAAS;YACjC,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO,IAAI,aAAa,MAAM,GAAG,SAAS;YACxC,aAAa;mBAAI;gBAAc;aAAO;QACxC;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,yBAAyB;IACzB,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA,MACrC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,CAAC,aAAa,QAAQ,CAAC,IAAI,GAAG;IAGhC,eAAe;IACf,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QACrD,OAAO,YAAY,UAAU,KAAK,GAAG;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,8OAAC;wBAAK,WAAU;kCACb,EAAE,kBAAkB;4BAAE,OAAO,aAAa,MAAM;4BAAE,KAAK;wBAAQ;;;;;;;;;;;;YAKnE,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;gCAEC,WAAU;;oCAET,oBAAoB;kDACrB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCACd,EAAE,mBAAmB;gCAAE,KAAK;4BAAQ;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAa,eAAe,EAAE;oCAC9B,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,SAAS,IAAM,UAAU;oCACzB,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAInB,CAAC,UAAU,UAAU,mBACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM,GAAG,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,aAAa,aAAa,MAAM,IAAI;gDAE1C,qBACE,8OAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,UAAU,IAAI,GAAG;wDACjB,cAAc;wDACd,UAAU;oDACZ;oDACA,UAAU;oDACV,WAAW,CAAC;;8BAEV,EAAE,aACE,iDACA,iCACH;4BACH,CAAC;8DAED,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd,IAAI,KAAK;;;;;;;mDAlBP,IAAI,GAAG;;;;;4CAsBlB;;;;;;wCAED,aAAa,MAAM,GAAG,oBACrB,8OAAC;4CAAE,WAAU;sDACV,EAAE,cAAc;gDAAE,OAAO,aAAa,MAAM;4CAAC;;;;;;;;;;;yDAKpD,8OAAC;oCAAI,WAAU;8CACZ,aAAa,EAAE,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlD,CAAC,UAAU,UAAU,mBACpB,8OAAC;gBACC,WAAU;gBACV,SAAS;oBACP,UAAU;oBACV,cAAc;gBAChB;;;;;;YAKH,aAAa,MAAM,IAAI,yBACtB,8OAAC;gBAAE,WAAU;0BACV,EAAE,kBAAkB;oBAAE,KAAK;gBAAQ;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n}\n\n// 生成模糊占位符数据URL\nfunction generateBlurDataURL(): string {\n  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==';\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n}: OptimizedImageProps) {\n  const imageProps = {\n    src,\n    alt,\n    className,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <Image\n          {...imageProps}\n          fill\n          style={{ objectFit: 'contain', padding: 2 }}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <Image\n      {...imageProps}\n      width={width}\n      height={height}\n    />\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 52, height: 52 },\n  toolLogoLarge: { width: 128, height: 128 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '52px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;AACA;;;AAgBA,eAAe;AACf,SAAS;IACP,OAAO;AACT;AAEe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACnB;IACpB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gBACH,GAAG,UAAU;gBACd,IAAI;gBACJ,OAAO;oBAAE,WAAW;oBAAW,SAAS;gBAAE;;;;;;;;;;;IAIlD;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACH,GAAG,UAAU;QACd,OAAO;QACP,QAAQ;;;;;;AAGd;AAGO,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAK,QAAQ;IAAI;IACzC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, Fragment, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport LoadingSpinner from '@/components/LoadingSpinner';\n\nimport SuccessMessage from '@/components/SuccessMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';\nimport { Tool } from '@/lib/api';\nimport {\n  Upload,\n  Link as LinkIcon,\n  Info,\n  ArrowLeft\n} from 'lucide-react';\nimport { Link } from '@/i18n/routing';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport TagSelector from '@/components/TagSelector';\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from '../ui/OptimizedImage';\nimport { Locale } from '@/i18n/config';\n\ninterface CategoryOption {\n  value: string;\n  label: string;\n}\n\ninterface SubmitFormClientProps {\n  categoryOptions: CategoryOption[];\n  // 编辑模式相关props\n  isEditMode?: boolean;\n  toolId?: string;\n  initialTool?: Tool;\n}\n\nexport default function SubmitFormClient({\n  categoryOptions,\n  isEditMode = false,\n  toolId,\n  initialTool,\n}: SubmitFormClientProps) {\n  // const t = getTranslations({ locale, namespace: 'submit' });\n  const t = useTranslations('submit');\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const [tool, setTool] = useState<Tool | null>(initialTool || null);\n  const [loading, setLoading] = useState(isEditMode && !initialTool);\n  const [formData, setFormData] = useState({\n    name: '',\n    tagline: '',\n    description: '',\n    website: '',\n    logoFile: null as File | null,\n    category: '',\n    tags: [] as string[],\n    pricing: ''\n  });\n\n  const [logoPreview, setLogoPreview] = useState<string | null>(null);\n  const [logoUrl, setLogoUrl] = useState<string>('');\n  const [uploadingLogo, setUploadingLogo] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [submitMessage, setSubmitMessage] = useState('');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n\n  // 获取工具信息（编辑模式）\n  useEffect(() => {\n    if (!isEditMode || !toolId || initialTool) return;\n\n    const fetchToolInfo = async () => {\n      try {\n        const response = await fetch(`/api/tools/${toolId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          const toolData = data.data;\n          setTool(toolData);\n\n          // 填充表单数据\n          setFormData({\n            name: toolData.name || '',\n            tagline: toolData.tagline || '',\n            description: toolData.description || '',\n            website: toolData.website || '',\n            logoFile: null,\n            category: toolData.category || '',\n            tags: toolData.tags || [],\n            pricing: toolData.pricing || ''\n          });\n\n          setLogoUrl(toolData.logo || '');\n          setLogoPreview(toolData.logo || '');\n        } else {\n          setSubmitStatus('error');\n          setSubmitMessage(data.message || '获取工具信息失败');\n        }\n      } catch (error) {\n        console.error('获取工具信息失败:', error);\n        setSubmitStatus('error');\n        setSubmitMessage('网络错误，请重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (session) {\n      fetchToolInfo();\n    } else if (status !== 'loading') {\n      setLoading(false);\n    }\n  }, [toolId, session, status, isEditMode, initialTool]);\n\n  // 初始化表单数据（编辑模式）\n  useEffect(() => {\n    if (isEditMode && initialTool) {\n      setTool(initialTool);\n      setFormData({\n        name: initialTool.name || '',\n        tagline: initialTool.tagline || '',\n        description: initialTool.description || '',\n        website: initialTool.website || '',\n        logoFile: null,\n        category: initialTool.category || '',\n        tags: initialTool.tags || [],\n        pricing: initialTool.pricing || ''\n      });\n      setLogoUrl(initialTool.logo || '');\n      setLogoPreview(initialTool.logo || '');\n      setLoading(false);\n    }\n  }, [isEditMode, initialTool]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      setFormData(prev => ({\n        ...prev,\n        logoFile: file\n      }));\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleTagsChange = (selectedTags: string[]) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: selectedTags\n    }));\n  };\n\n  // const validateForm = () => {\n  //   const newErrors: Record<string, string> = {};\n\n  //   const requiredFields = [\n  //     { key: 'name', label: t('form.tool_name') },\n  //     { key: 'tagline', label: t('form.tagline') },\n  //     { key: 'description', label: t('form.description') },\n  //     { key: 'websiteUrl', label: t('form.website_url') },\n  //     { key: 'category', label: t('form.category') },\n  //     { key: 'pricingModel', label: t('form.pricing_model') },\n  //   ];\n  //   const missingFields = requiredFields.filter(field => !formData[field.key as keyof typeof formData]);\n\n  //   if (missingFields.length > 0) {\n  //     alert(\n  //     t('form.missing_required_fields') +\n  //     ':\\n' +\n  //     missingFields.map(field => `- ${field.label}`).join('\\n')\n  //     );\n  //     return false;\n  //   }\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) newErrors.name = t('form.tool_name') + ' is required';\n    if (!formData.description.trim()) newErrors.description = t('form.description') + ' is required';\n    if (!formData.website.trim()) newErrors.website = t('form.website_url') + ' is required';\n    if (!formData.category) newErrors.category = t('form.category') + ' is required';\n    if (!formData.pricing) newErrors.pricing = t('form.pricing_model') + ' is required';\n\n    // URL validation\n    if (formData.website && !formData.website.match(/^https?:\\/\\/.+/)) {\n      newErrors.website = t('form.website_url_placeholder');\n    }\n\n    // 验证 logo（编辑模式下如果已有logo则不强制要求新上传）\n    if (!isEditMode && !formData.logoFile) {\n      newErrors.logo = t('form.logo_required');\n    }\n\n    if (formData.tags.length === 0) {\n      newErrors.tags = t('form.tags_placeholder');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!session?.user?.email) {\n      setIsLoginModalOpen(true);\n      return;\n    }\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // 处理 logo 上传\n      let finalLogoUrl = logoUrl;\n      if (formData.logoFile) {\n        const logoFormData = new FormData();\n        logoFormData.append('logo', formData.logoFile);\n\n        const uploadResponse = await fetch('/api/upload/logo', {\n          method: 'POST',\n          body: logoFormData,\n        });\n\n        if (uploadResponse.ok) {\n          const uploadResult = await uploadResponse.json();\n          finalLogoUrl = uploadResult.data.url;\n        } else {\n          const errorData = await uploadResponse.json();\n          throw new Error(errorData.message || 'Logo upload failed');\n        }\n      }\n\n      if (isEditMode && toolId) {\n        // 编辑模式：更新工具\n        const updateData = {\n          name: formData.name,\n          tagline: formData.tagline,\n          description: formData.description,\n          website: formData.website,\n          logo: finalLogoUrl || undefined,\n          category: formData.category,\n          tags: formData.tags,\n          pricing: formData.pricing\n        };\n\n        const response = await fetch(`/api/tools/${toolId}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(updateData),\n        });\n\n        const data = await response.json();\n\n        if (data.success) {\n          setSubmitStatus('success');\n          setSubmitMessage('工具信息更新成功！');\n          // 跳转回提交的工具列表\n          setTimeout(() => {\n            router.push('/profile/submitted');\n          }, 2000);\n        } else {\n          setSubmitStatus('error');\n          setSubmitMessage(data.error || 'Update failed, please retry');\n        }\n      } else {\n        // 新建模式：提交工具\n        const submitData = {\n          name: formData.name,\n          tagline: formData.tagline,\n          description: formData.description,\n          website: formData.website,\n          logoUrl: finalLogoUrl,\n          category: formData.category,\n          tags: formData.tags,\n          pricing: formData.pricing,\n        };\n\n        const response = await fetch('/api/tools/submit', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(submitData),\n        });\n\n        if (response.ok) {\n          const result = await response.json();\n          setSubmitStatus('success');\n          setSubmitMessage(t('form.success_message'));\n\n          // 重定向到新的工具信息提交成功页面\n          setTimeout(() => {\n            router.push(`/submit/tool-info-success?toolId=${result.data.toolId}`);\n          }, 2000);\n        } else {\n          const errorData = await response.json();\n          throw new Error(errorData.message || 'Submission failed');\n        }\n      }\n    } catch (error) {\n      console.error('Submit error:', error);\n      setSubmitStatus('error');\n      setSubmitMessage((error as Error).message + '. ' +t('form.error_message'));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 加载状态\n  if (status === 'loading' || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  // 未登录状态\n  if (!session) {\n    return (\n      <Fragment>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {t('auth.login_required')}\n            </h1>\n            <p className=\"text-gray-600 mb-6\">\n              {t('auth.login_to_submit')}\n            </p>\n            <button\n              onClick={() => setIsLoginModalOpen(true)}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700\"\n            >\n              {t('auth.login')}\n            </button>\n          </div>\n        </div>\n        <LoginModal\n          isOpen={isLoginModalOpen}\n          onClose={() => setIsLoginModalOpen(false)}\n        />\n      </Fragment>\n    );\n  }\n\n  // 编辑模式下工具不存在\n  if (isEditMode && !tool) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Tool not found</h1>\n          <p className=\"text-gray-600 mb-6\">The tool you want to edit does not exist or has been deleted.</p>\n          <Link\n            href=\"/profile/submitted\"\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700\"\n          >\n            Back to Tools\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  if (submitStatus === 'success') {\n    return (\n      <div className=\"max-w-2xl mx-auto\">\n        <SuccessMessage message={submitMessage || t('form.success_message')} />\n      </div>\n    );\n  }\n\n  return (\n    <Fragment>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        {isEditMode && (\n          <div className=\"mb-8\">\n            <Link\n              href=\"/profile/submitted\"\n              className=\"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Tools\n            </Link>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Edit Tool</h1>\n            <p className=\"text-gray-600 mt-2\">\n              Update your tool information to help more users understand your product.\n            </p>\n          </div>\n        )}\n\n        {!isEditMode && (\n          <div className=\"text-center mb-8\">\n            <div className=\"flex justify-center mb-4\">\n              <div className=\"p-3 bg-blue-100 rounded-full\">\n                <Upload className=\"h-8 w-8 text-blue-600\" />\n              </div>\n            </div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              {t('title')}\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              {t('subtitle')}\n            </p>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"max-w-4xl mx-auto space-y-8\">\n          {/* 基本信息 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n              <Info className=\"h-5 w-5 mr-2 text-blue-600\" />\n              {t('form.basic_info')}\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 工具名称 */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.tool_name')} <span className=\"text-red-500\">*</span>\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  placeholder={t('form.tool_name_placeholder')}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                />\n              </div>\n\n              {/* 工具标语 */}\n              <div>\n                <label htmlFor=\"tagline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.tagline')} <span className=\"text-red-500\">*</span>\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"tagline\"\n                  name=\"tagline\"\n                  value={formData.tagline}\n                  onChange={handleInputChange}\n                  placeholder={t('form.tagline_placeholder')}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n\n            {/* 详细描述 */}\n            <div className=\"mt-6\">\n              <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.description')} <span className=\"text-red-500\">*</span>\n              </label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                placeholder={t('form.description_placeholder')}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n            </div>\n\n            {/* 官方网站 */}\n            <div className=\"mt-6\">\n              <label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.website_url')} <span className=\"text-red-500\">*</span>\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <LinkIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                {\n                  !isEditMode &&\n                  <input\n                    type=\"url\"\n                    id=\"website\"\n                    name=\"website\"\n                    value={formData.website}\n                    onChange={handleInputChange}\n                    placeholder={isEditMode ? \"https://example.com\" : t('form.website_url_placeholder')}\n                    className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.website ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                    required\n                  />\n                }\n                {\n                  isEditMode && <div className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600\">\n                    {formData.website}\n                  </div>\n                }\n              </div>\n              {errors.website && <p className=\"text-red-600 text-sm mt-1\">{errors.website}</p>}\n            </div>\n\n            {/* Logo 上传 */}\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.logo_upload')} {!isEditMode && <span className=\"text-red-500\">*</span>}\n              </label>\n              <div className=\"flex items-start space-x-6\">\n                <div className=\"flex-1\">\n                  {\n                    // isEditMode ? (\n                    <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors\">\n                      <input\n                        type=\"file\"\n                        accept=\"image/*\"\n                        onChange={(e) => {\n                          const file = e.target.files?.[0];\n                          if (file) {\n                            handleLogoChange(e);\n                          }\n                        }}\n                        className=\"hidden\"\n                        id=\"logo-upload\"\n                        required={!isEditMode}\n                      />\n                      <label htmlFor=\"logo-upload\" className=\"cursor-pointer\">\n                        <Upload className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                        <p className=\"text-sm text-gray-600\">\n                          {uploadingLogo ? t('form.uploading') : t('form.click_to_upload')}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {t('form.logo_upload_hint')}\n                        </p>\n                      </label>\n                    </div>\n                    // ) : (\n                    //   <div className=\"relative\">\n                    //     <input\n                    //       type=\"file\"\n                    //       id=\"logo\"\n                    //       name=\"logo\"\n                    //       accept=\"image/*\"\n                    //       onChange={handleLogoChange}\n                    //       className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    //       required={!isEditMode}\n                    //     />\n                    //     <p className=\"mt-1 text-sm text-gray-500\">\n                    //       {t('form.logo_upload_hint')}\n                    //     </p>\n                    //   </div>\n                    // )\n                  }\n                  {errors.logo && <p className=\"text-red-600 text-sm mt-1\">{errors.logo}</p>}\n                </div>\n                {/* {logoPreview && (\n                  <div className=\"flex-shrink-0\">\n                    <div className={`border border-gray-300 rounded-md overflow-hidden ${'w-24 h-24'}`}>\n                      <img\n                        src={logoPreview}\n                        alt={t('form.logo_preview')}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  </div>\n                )} */}\n                {\n                  logoPreview && <OptimizedImage\n                    alt={\"app logo\"}\n                    src={logoPreview}\n                    width={ImageSizes.toolLogo.width}\n                    height={ImageSizes.toolLogo.height}\n                    className=\"rounded-lg object-cover\"\n                    sizes={ResponsiveSizes.toolLogo}\n                    placeholder=\"blur\"\n                  />\n                }\n              </div>\n            </div>\n          </div>\n\n          {/* 分类和定价 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              {t('form.category_and_pricing')}\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 工具分类 */}\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.category')} <span className=\"text-red-500\">*</span>\n                </label>\n                <select\n                  id=\"category\"\n                  name=\"category\"\n                  value={formData.category}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">{t('form.category_placeholder')}</option>\n                  {categoryOptions.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 价格模式 */}\n              <div>\n                <label htmlFor=\"pricing\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.pricing_model')} <span className=\"text-red-500\">*</span>\n                </label>\n                <select\n                  id=\"pricing\"\n                  name=\"pricing\"\n                  value={formData.pricing}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.pricing ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                  required\n                >\n                  <option value=\"\">{t('form.pricing_placeholder')}</option>\n                  {TOOL_PRICING_FORM_OPTIONS.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {t(`form.${option.value}`)}\n                    </option>\n                  ))}\n                </select>\n                {errors.pricing && <p className=\"text-red-600 text-sm mt-1\">{errors.pricing}</p>}\n              </div>\n            </div>\n\n            {/* 选择标签 */}\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.tags')} <span className=\"text-red-500\">*</span>\n              </label>\n              <TagSelector\n                selectedTags={formData.tags}\n                onTagsChange={handleTagsChange}\n                maxTags={MAX_TAGS_COUNT}\n                placeholder={t('form.tags_placeholder')}\n              />\n              {errors.tags && <p className=\"text-red-600 text-sm mt-1\">{errors.tags}</p>}\n            </div>\n          </div>\n\n          {/* 提交指南 - 仅在新建模式显示 */}\n          {!isEditMode && (\n            <div className=\"bg-blue-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-4\">\n                {t('form.guidelines_title')}\n              </h3>\n              <ul className=\"space-y-2 text-blue-800\">\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_1')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_2')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_3')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_4')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_5')}\n                </li>\n              </ul>\n            </div>\n          )}\n\n          {/* 提交按钮 */}\n          <div className={isEditMode ? \"flex justify-end\" : \"flex justify-center\"}>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${isEditMode ? 'px-8 py-3 text-base' : 'px-8 py-3 text-base'\n                }`}\n            >\n              {isSubmitting ? (\n                <Fragment>\n                  <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  {t('form.submitting')}\n                </Fragment>\n              ) : (\n                <Fragment>\n                  <Upload className=\"h-5 w-5 mr-2\" />\n                  {t('form.submit_button')}\n                </Fragment>\n              )}\n            </button>\n          </div>\n        </form>\n\n        {/* Status Messages */}\n        {submitStatus === 'error' && (\n          <div className=\"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800\">{submitMessage}</p>\n          </div>\n        )}\n      </div>\n\n      {/* 登录模态框 */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Fragment>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AArBA;;;;;;;;;;;;;;;AAqCe,SAAS,iBAAiB,EACvC,eAAe,EACf,aAAa,KAAK,EAClB,MAAM,EACN,WAAW,EACW;IACtB,8DAA8D;IAC9D,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,eAAe;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,CAAC;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,MAAM,EAAE;QACR,SAAS;IACX;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc,CAAC,UAAU,aAAa;QAE3C,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;gBACnD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,MAAM,WAAW,KAAK,IAAI;oBAC1B,QAAQ;oBAER,SAAS;oBACT,YAAY;wBACV,MAAM,SAAS,IAAI,IAAI;wBACvB,SAAS,SAAS,OAAO,IAAI;wBAC7B,aAAa,SAAS,WAAW,IAAI;wBACrC,SAAS,SAAS,OAAO,IAAI;wBAC7B,UAAU;wBACV,UAAU,SAAS,QAAQ,IAAI;wBAC/B,MAAM,SAAS,IAAI,IAAI,EAAE;wBACzB,SAAS,SAAS,OAAO,IAAI;oBAC/B;oBAEA,WAAW,SAAS,IAAI,IAAI;oBAC5B,eAAe,SAAS,IAAI,IAAI;gBAClC,OAAO;oBACL,gBAAgB;oBAChB,iBAAiB,KAAK,OAAO,IAAI;gBACnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,gBAAgB;gBAChB,iBAAiB;YACnB,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,SAAS;YACX;QACF,OAAO,IAAI,WAAW,WAAW;YAC/B,WAAW;QACb;IACF,GAAG;QAAC;QAAQ;QAAS;QAAQ;QAAY;KAAY;IAErD,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,aAAa;YAC7B,QAAQ;YACR,YAAY;gBACV,MAAM,YAAY,IAAI,IAAI;gBAC1B,SAAS,YAAY,OAAO,IAAI;gBAChC,aAAa,YAAY,WAAW,IAAI;gBACxC,SAAS,YAAY,OAAO,IAAI;gBAChC,UAAU;gBACV,UAAU,YAAY,QAAQ,IAAI;gBAClC,MAAM,YAAY,IAAI,IAAI,EAAE;gBAC5B,SAAS,YAAY,OAAO,IAAI;YAClC;YACA,WAAW,YAAY,IAAI,IAAI;YAC/B,eAAe,YAAY,IAAI,IAAI;YACnC,WAAW;QACb;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;gBACZ,CAAC;YAED,OAAO;YACP,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,eAAe,EAAE,MAAM,EAAE;YAC3B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM;YACR,CAAC;IACH;IAEA,+BAA+B;IAC/B,kDAAkD;IAElD,6BAA6B;IAC7B,mDAAmD;IACnD,oDAAoD;IACpD,4DAA4D;IAC5D,2DAA2D;IAC3D,sDAAsD;IACtD,+DAA+D;IAC/D,OAAO;IACP,yGAAyG;IAEzG,oCAAoC;IACpC,aAAa;IACb,0CAA0C;IAC1C,cAAc;IACd,gEAAgE;IAChE,SAAS;IACT,oBAAoB;IACpB,MAAM;IAEN,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG,EAAE,oBAAoB;QAClE,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG,EAAE,sBAAsB;QAClF,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG,EAAE,sBAAsB;QAC1E,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG,EAAE,mBAAmB;QAClE,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG,EAAE,wBAAwB;QAErE,iBAAiB;QACjB,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,mBAAmB;YACjE,UAAU,OAAO,GAAG,EAAE;QACxB;QAEA,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,SAAS,QAAQ,EAAE;YACrC,UAAU,IAAI,GAAG,EAAE;QACrB;QAEA,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAC9B,UAAU,IAAI,GAAG,EAAE;QACrB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,oBAAoB;YACpB;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,aAAa;YACb,IAAI,eAAe;YACnB,IAAI,SAAS,QAAQ,EAAE;gBACrB,MAAM,eAAe,IAAI;gBACzB,aAAa,MAAM,CAAC,QAAQ,SAAS,QAAQ;gBAE7C,MAAM,iBAAiB,MAAM,MAAM,oBAAoB;oBACrD,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,eAAe,EAAE,EAAE;oBACrB,MAAM,eAAe,MAAM,eAAe,IAAI;oBAC9C,eAAe,aAAa,IAAI,CAAC,GAAG;gBACtC,OAAO;oBACL,MAAM,YAAY,MAAM,eAAe,IAAI;oBAC3C,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;gBACvC;YACF;YAEA,IAAI,cAAc,QAAQ;gBACxB,YAAY;gBACZ,MAAM,aAAa;oBACjB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,MAAM,gBAAgB;oBACtB,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;gBAC3B;gBAEA,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;oBACnD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB;oBAChB,iBAAiB;oBACjB,aAAa;oBACb,WAAW;wBACT,OAAO,IAAI,CAAC;oBACd,GAAG;gBACL,OAAO;oBACL,gBAAgB;oBAChB,iBAAiB,KAAK,KAAK,IAAI;gBACjC;YACF,OAAO;gBACL,YAAY;gBACZ,MAAM,aAAa;oBACjB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,SAAS;oBACT,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;gBAC3B;gBAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,gBAAgB;oBAChB,iBAAiB,EAAE;oBAEnB,mBAAmB;oBACnB,WAAW;wBACT,OAAO,IAAI,CAAC,CAAC,iCAAiC,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE;oBACtE,GAAG;gBACL,OAAO;oBACL,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB;YAChB,iBAAiB,AAAC,MAAgB,OAAO,GAAG,OAAM,EAAE;QACtD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,OAAO;IACP,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,QAAQ;IACR,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,qMAAA,CAAA,WAAQ;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,8OAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;;8BAIT,8OAAC,wIAAA,CAAA,UAAU;oBACT,QAAQ;oBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;IAI3C;IAEA,aAAa;IACb,IAAI,cAAc,CAAC,MAAM;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,sHAAA,CAAA,OAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;gBAAC,SAAS,iBAAiB,EAAE;;;;;;;;;;;IAGlD;IAEA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;gBAAI,WAAU;;oBAEZ,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sHAAA,CAAA,OAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAMrC,CAAC,4BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGtB,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAKT,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,EAAE;;;;;;;kDAGL,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;;4DAC7B,EAAE;4DAAkB;0EAAC,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEvD,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,aAAa,EAAE;wDACf,WAAU;wDACV,QAAQ;;;;;;;;;;;;0DAKZ,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;;4DAChC,EAAE;4DAAgB;0EAAC,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAErD,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,aAAa,EAAE;wDACf,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;;oDACpC,EAAE;oDAAoB;kEAAC,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAa,EAAE;gDACf,MAAM;gDACN,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;;oDAChC,EAAE;oDAAoB;kEAAC,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;;;;;;oDAGpB,CAAC,4BACD,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,aAAa,aAAa,wBAAwB,EAAE;wDACpD,WAAW,CAAC,6HAA6H,EAAE,OAAO,OAAO,GAAG,mBAAmB,mBAC3K;wDACJ,QAAQ;;;;;;oDAIV,4BAAc,8OAAC;wDAAI,WAAU;kEAC1B,SAAS,OAAO;;;;;;;;;;;;4CAItB,OAAO,OAAO,kBAAI,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,OAAO;;;;;;;;;;;;kDAI7E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;oDACd,EAAE;oDAAoB;oDAAE,CAAC,4BAAc,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEzE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAEX,iBAAiB;0EACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,QAAO;wEACP,UAAU,CAAC;4EACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;4EAChC,IAAI,MAAM;gFACR,iBAAiB;4EACnB;wEACF;wEACA,WAAU;wEACV,IAAG;wEACH,UAAU,CAAC;;;;;;kFAEb,8OAAC;wEAAM,SAAQ;wEAAc,WAAU;;0FACrC,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;gFAAE,WAAU;0FACV,gBAAgB,EAAE,oBAAoB,EAAE;;;;;;0FAE3C,8OAAC;gFAAE,WAAU;0FACV,EAAE;;;;;;;;;;;;;;;;;;4DAqBV,OAAO,IAAI,kBAAI,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,IAAI;;;;;;;;;;;;oDAcrE,6BAAe,8OAAC,0IAAA,CAAA,UAAc;wDAC5B,KAAK;wDACL,KAAK;wDACL,OAAO,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;wDAChC,QAAQ,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;wDAClC,WAAU;wDACV,OAAO,0IAAA,CAAA,kBAAe,CAAC,QAAQ;wDAC/B,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAQtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAGL,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;;4DACjC,EAAE;4DAAiB;0EAAC,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtD,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;wDACV,QAAQ;;0EAER,8OAAC;gEAAO,OAAM;0EAAI,EAAE;;;;;;4DACnB,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,KAAK;mEADF,OAAO,KAAK;;;;;;;;;;;;;;;;;0DAQ/B,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;;4DAChC,EAAE;4DAAsB;0EAAC,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE3D,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAW,CAAC,uHAAuH,EAAE,OAAO,OAAO,GAAG,mBAAmB,mBACrK;wDACJ,QAAQ;;0EAER,8OAAC;gEAAO,OAAM;0EAAI,EAAE;;;;;;4DACnB,2HAAA,CAAA,4BAAyB,CAAC,GAAG,CAAC,CAAC,uBAC9B,8OAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;mEADd,OAAO,KAAK;;;;;;;;;;;oDAK5B,OAAO,OAAO,kBAAI,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAK/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;oDACd,EAAE;oDAAa;kEAAC,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAElD,8OAAC,iIAAA,CAAA,UAAW;gDACV,cAAc,SAAS,IAAI;gDAC3B,cAAc;gDACd,SAAS,wHAAA,CAAA,iBAAc;gDACvB,aAAa,EAAE;;;;;;4CAEhB,OAAO,IAAI,kBAAI,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;;4BAKxE,CAAC,4BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;;;;;;;;;;;;;0CAOX,8OAAC;gCAAI,WAAW,aAAa,qBAAqB;0CAChD,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,uQAAuQ,EAAE,aAAa,wBAAwB,uBACtT;8CAEH,6BACC,8OAAC,qMAAA,CAAA,WAAQ;;0DACP,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;gDAAK,WAAU;;;;;;4CACnC,EAAE;;;;;;6DAGL,8OAAC,qMAAA,CAAA,WAAQ;;0DACP,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,EAAE;;;;;;;;;;;;;;;;;;;;;;;oBAQZ,iBAAiB,yBAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMnC,8OAAC,wIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C", "debugId": null}}]}