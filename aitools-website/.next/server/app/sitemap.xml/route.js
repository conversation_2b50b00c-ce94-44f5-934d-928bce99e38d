(()=>{var e={};e.id=5475,e.ids=[5475],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var o={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function n(e){if(!r.o(o,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=o[e],n=t[0];return r.e(t[1]).then(()=>r.t(n,19))}n.keys=()=>Object.keys(o),n.id=3845,e.exports=n},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>o,q:()=>n});let o=["en","zh"],n="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(35471),n=r(12688);let i=(0,o.A)(async({locale:e})=>(e&&n.IB.find(t=>t.toString()===e?.toString())||(e=n.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(56037),n=r.n(o),i=r(60366);let a=new o.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:i.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({launchDate:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let s=n().models.Tool||n().model("Tool",a)},34490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var o={};r.r(o),r.d(o,{GET:()=>d});var n=r(96559),i=r(48088),a=r(37719),s=r(32190),l=r(75745),c=r(30762),u=r(60366);async function d(){try{await (0,l.A)();let e=await c.A.find({status:"approved",selectedLaunchDate:{$lte:new Date}}).select("_id name selectedLaunchDate updatedAt").lean(),t=process.env.NEXT_PUBLIC_BASE_URL||"https://www.aitools.pub",r=new Date().toISOString(),o=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">

  <!-- 主页 -->
  <url>
    <loc>${t}</loc>
    <lastmod>${r}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- 工具目录页 -->
  <url>
    <loc>${t}/tools</loc>
    <lastmod>${r}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- 分类总览页 -->
  <url>
    <loc>${t}/categories</loc>
    <lastmod>${r}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- 各个分类页面 -->
  ${u.ut.map(e=>`
  <url>
    <loc>${t}/categories/${e}</loc>
    <lastmod>${r}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`).join("")}

  <!-- 静态页面 -->
  <url>
    <loc>${t}/about</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>

  <url>
    <loc>${t}/contact</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>

  <url>
    <loc>${t}/privacy</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <url>
    <loc>${t}/terms</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <!-- 工具详情页 -->
  ${e.map(e=>`
  <url>
    <loc>${t}/tools/${e._id}</loc>
    <lastmod>${e.updatedAt?new Date(e.updatedAt).toISOString():new Date(e.launchDate).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`).join("")}

</urlset>`;return new s.NextResponse(o,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(e){return console.error("Error generating sitemap:",e),new s.NextResponse("Internal Server Error",{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"route",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:y}=p;function h(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var o=r(78521),n=r(60687);function i({locale:e,...t}){if(!e)throw Error(void 0);return(0,n.jsx)(o.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>a,PZ:()=>s,RI:()=>c,ut:()=>l});var o=r(64348);let n=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function i(e){let t=await (0,o.A)({locale:e||"en",namespace:"categories"});return n.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function a(e){return(await i(e)).map(e=>({value:e.slug,label:e.name}))}async function s(e,t){return(await i(t)).find(t=>t.slug===e)}let l=n.map(e=>e.slug),c=n.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(61120),n=r(92440),i=r(84604),a=(0,o.cache)(function(e,t){return function({_cache:e=(0,i.d)(),_formatters:t=(0,i.b)(e),getMessageFallback:r=i.f,messages:o,namespace:n,onError:a=i.g,...s}){return function({messages:e,namespace:t,...r},o){return e=e["!"],t=(0,i.r)(t,"!"),(0,i.e)({...r,messages:e,namespace:t})}({...s,onError:a,cache:e,formatters:t,getMessageFallback:r,messages:{"!":o},namespace:n?`!.${n}`:"!"},"!")}({...e,namespace:t})}),s=(0,o.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),a(await (0,n.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(56037),n=r.n(o);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let s=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(i,{bufferCommands:!1}));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4243,4999,9658,580],()=>r(34490));module.exports=o})();