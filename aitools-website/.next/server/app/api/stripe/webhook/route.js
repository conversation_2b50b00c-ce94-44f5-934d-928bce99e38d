(()=>{var e={};e.id=3287,e.ids=[3287,6706],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var n={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function a(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],a=t[0];return r.e(t[1]).then(()=>r.t(a,19))}a.keys=()=>Object.keys(n),a.id=3845,e.exports=a},9865:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>E,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>v});var n={};r.r(n),r.d(n,{POST:()=>m});var a=r(96559),i=r(48088),o=r(37719),s=r(32190),c=r(44999),d=r(75745),u=r(31098),l=r(30762),p=r(96706);async function m(e){try{let t,r=await e.text(),n=(await (0,c.headers)()).get("stripe-signature");if(!n)return s.NextResponse.json({error:"Missing stripe-signature header"},{status:400});let a=process.env.STRIPE_WEBHOOK_SECRET;if(!a)return console.error("STRIPE_WEBHOOK_SECRET is not configured"),s.NextResponse.json({error:"Webhook secret not configured"},{status:500});try{t=(0,p.ZW)(r,n,a)}catch(e){return console.error("Webhook signature verification failed:",e),s.NextResponse.json({error:"Invalid signature"},{status:400})}switch(await (0,d.A)(),t.type){case"payment_intent.succeeded":await g(t.data.object);break;case"payment_intent.payment_failed":await y(t.data.object);break;case"payment_intent.canceled":await f(t.data.object);break;default:console.log(`Unhandled event type: ${t.type}`)}return s.NextResponse.json({received:!0})}catch(e){return console.error("Webhook processing error:",e),s.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function g(e){console.log("handlePaymentSucceeded called:...........",JSON.stringify(e,null,2));try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await u.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.markAsPaid(),r.stripePaymentIntentId=e.id,r.paymentMethod="stripe",r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created)},await r.save(),await l.A.findByIdAndUpdate(r.toolId,{$set:{paymentStatus:"completed",paidAt:new Date,status:"pending"}}),console.log(`Payment succeeded for order: ${t}`)}catch(e){console.error("Error handling payment succeeded:",e)}}async function y(e){try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await u.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.markAsFailed(),r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created),failureReason:e.last_payment_error?.message},await r.save(),console.log(`Payment failed for order: ${t}`)}catch(e){console.error("Error handling payment failed:",e)}}async function f(e){try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await u.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.cancel(),r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created)},await r.save(),console.log(`Payment canceled for order: ${t}`)}catch(e){console.error("Error handling payment canceled:",e)}}let h=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/stripe/webhook/route",pathname:"/api/stripe/webhook",filename:"route",bundlePath:"app/api/stripe/webhook/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:I,workUnitAsyncStorage:v,serverHooks:E}=h;function w(){return(0,o.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:v})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>n,q:()=>a});let n=["en","zh"],a="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(35471),a=r(12688);let i=(0,n.A)(async({locale:e})=>(e&&a.IB.find(t=>t.toString()===e?.toString())||(e=a.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(56037),a=r.n(n),i=r(60366);let o=new n.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:i.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({status:1,isActive:1}),o.index({category:1,status:1}),o.index({tags:1,status:1}),o.index({submittedBy:1}),o.index({launchDate:-1}),o.index({views:-1}),o.index({likes:-1}),o.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let s=a().models.Tool||a().model("Tool",o)},31098:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(56037),a=r.n(n);let i=new n.Schema({userId:{type:n.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:n.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});i.index({userId:1,createdAt:-1}),i.index({toolId:1}),i.index({status:1}),i.index({paymentIntentId:1}),i.index({paymentSessionId:1}),i.index({stripePaymentIntentId:1}),i.index({stripeCustomerId:1}),i.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),i.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),i.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},i.methods.markAsFailed=function(){return this.status="failed",this.save()},i.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},i.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let o=a().models.Order||a().model("Order",i)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(78521),a=r(60687);function i({locale:e,...t}){if(!e)throw Error(void 0);return(0,a.jsx)(n.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,PZ:()=>s,RI:()=>d,ut:()=>c});var n=r(64348);let a=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function i(e){let t=await (0,n.A)({locale:e||"en",namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await i(e)).map(e=>({value:e.slug,label:e.name}))}async function s(e,t){return(await i(t)).find(t=>t.slug===e)}let c=a.map(e=>e.slug),d=a.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(61120),a=r(92440),i=r(84604),o=(0,n.cache)(function(e,t){return function({_cache:e=(0,i.d)(),_formatters:t=(0,i.b)(e),getMessageFallback:r=i.f,messages:n,namespace:a,onError:o=i.g,...s}){return function({messages:e,namespace:t,...r},n){return e=e["!"],t=(0,i.r)(t,"!"),(0,i.e)({...r,messages:e,namespace:t})}({...s,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":n},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),s=(0,n.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),o(await (0,a.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(56037),a=r.n(n);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let s=async function(){if(o.conn)return o.conn;o.promise||(o.promise=a().connect(i,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},79171:(e,t,r)=>{"use strict";r.d(t,{$g:()=>s,Ef:()=>i,S9:()=>c,kX:()=>n,mV:()=>o,mp:()=>d});let n={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",promotion:{enabled:!0,description:"限时优惠 - 前100个付费用户",discountPercent:50,remainingSlots:85},features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}};n.FREE_LAUNCH.description,n.FREE_LAUNCH.displayPrice,n.FREE_LAUNCH.features,n.PRIORITY_LAUNCH.description,n.PRIORITY_LAUNCH.displayPrice,n.PRIORITY_LAUNCH.features;let a={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}};a.FREE.value,a.FREE.label,a.FREEMIUM.value,a.FREEMIUM.label,a.PAID.value,a.PAID.label,a.FREE.value,a.FREE.label,a.FREEMIUM.value,a.FREEMIUM.label,a.PAID.value,a.PAID.label;let i=e=>{switch(e){case a.FREE.value:return a.FREE.color;case a.FREEMIUM.value:return a.FREEMIUM.color;case a.PAID.value:return a.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case a.FREE.value:return a.FREE.label;case a.FREEMIUM.value:return a.FREEMIUM.label;case a.PAID.value:return a.PAID.label;default:return e}},s=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,c=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,d=()=>{let e=n.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0}},79646:e=>{"use strict";e.exports=require("child_process")},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96706:(e,t,r)=>{"use strict";r.d(t,{ZW:()=>d,bw:()=>c,f:()=>o,stripe:()=>i});var n=r(97877),a=r(79171);let i=new n.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0});async function o(e,t="cny",r={}){try{return await i.paymentIntents.create({amount:e,currency:t,metadata:r,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function s(e,t,r={}){try{return await i.customers.create({email:e,name:t,metadata:r})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function c(e,t,r={}){try{let n=await i.customers.list({email:e,limit:1});if(n.data.length>0)return n.data[0];return await s(e,t,r)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function d(e,t,r){try{return i.webhooks.constructEvent(e,t,r)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}a.kX.PRIORITY_LAUNCH.productName,a.kX.PRIORITY_LAUNCH.stripeAmount,a.kX.PRIORITY_LAUNCH.stripeCurrency,a.kX.PRIORITY_LAUNCH.description,a.kX.PRIORITY_LAUNCH.features}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,4999,9658,580,7877],()=>r(9865));module.exports=n})();