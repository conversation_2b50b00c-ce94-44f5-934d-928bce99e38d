(()=>{var e={};e.id=8673,e.ids=[8673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var i=r(36344),s=r(65752),o=r(13581),n=r(75745),a=r(17063);let c={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,o.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let t=await a.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,s]=r.split(":");if(i!==e.token||s!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,n.A)();try{let i=await a.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new a.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},16194:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>p,PUT:()=>m});var s=r(96559),o=r(48088),n=r(37719),a=r(32190),c=r(35426),u=r(12909),l=r(75745),d=r(17063);async function p(e){try{let e=await (0,c.getServerSession)(u.N);if(!e?.user?.email)return a.NextResponse.json({success:!1,error:"未登录"},{status:401});await (0,l.A)();let t=await d.A.findOne({email:e.user.email}).select("-emailVerificationToken -emailVerificationExpires");if(!t)return a.NextResponse.json({success:!1,error:"用户不存在"},{status:404});return a.NextResponse.json({success:!0,data:{id:t._id.toString(),email:t.email,name:t.name,avatar:t.avatar,bio:t.bio,website:t.website,location:t.location,role:t.role,emailVerified:t.emailVerified,isActive:t.isActive,accounts:t.accounts.map(e=>({provider:e.provider,providerId:e.providerId})),createdAt:t.createdAt,lastLoginAt:t.lastLoginAt}})}catch(e){return console.error("Get user info error:",e),a.NextResponse.json({success:!1,error:"服务器错误，请稍后重试"},{status:500})}}async function m(e){try{let t=await (0,c.getServerSession)(u.N);if(!t?.user?.email)return a.NextResponse.json({success:!1,error:"未登录"},{status:401});await (0,l.A)();let{name:r,avatar:i,bio:s,website:o,location:n}=await e.json();if(r&&("string"!=typeof r||0===r.trim().length||r.length>100))return a.NextResponse.json({success:!1,error:"用户名格式不正确"},{status:400});if(i&&"string"!=typeof i)return a.NextResponse.json({success:!1,error:"头像URL格式不正确"},{status:400});if(s&&("string"!=typeof s||s.length>500))return a.NextResponse.json({success:!1,error:"个人简介不能超过500个字符"},{status:400});if(o&&("string"!=typeof o||o.trim()&&!/^https?:\/\/.+/.test(o.trim())))return a.NextResponse.json({success:!1,error:"网站地址格式不正确"},{status:400});if(n&&("string"!=typeof n||n.length>100))return a.NextResponse.json({success:!1,error:"所在地不能超过100个字符"},{status:400});let p={};void 0!==r&&(p.name=r.trim()),void 0!==i&&(p.avatar=i),void 0!==s&&(p.bio=s.trim()),void 0!==o&&(p.website=o.trim()),void 0!==n&&(p.location=n.trim());let m=await d.A.findOneAndUpdate({email:t.user.email},p,{new:!0,runValidators:!0}).select("-emailVerificationToken -emailVerificationExpires");if(!m)return a.NextResponse.json({success:!1,error:"用户不存在"},{status:404});return a.NextResponse.json({success:!0,message:"用户信息更新成功",data:{id:m._id.toString(),email:m.email,name:m.name,avatar:m.avatar,bio:m.bio,website:m.website,location:m.location,role:m.role,emailVerified:m.emailVerified}})}catch(e){return console.error("Update user info error:",e),a.NextResponse.json({success:!1,error:"服务器错误，请稍后重试"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:v,workUnitAsyncStorage:g,serverHooks:h}=f;function x(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:g})}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),s=r.n(i);let o=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[o],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},n.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let a=s().models.User||s().model("User",n)},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return s},describeStringPropertyAccess:function(){return i},wellKnownProperties:function(){return o}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function s(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),s=r.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=s().connect(o,{bufferCommands:!1}));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,580,3136],()=>r(16194));module.exports=i})();