(()=>{var e={};e.id=2455,e.ids=[2455],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(60687),a=t(93613),l=t(11860);function i({message:e,onClose:r,className:t=""}){return(0,s.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:e})}),r&&(0,s.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60687);function a({size:e="md",className:r=""}){return(0,s.jsx)("div",{className:`flex justify-center items-center ${r}`,children:(0,s.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},56976:(e,r,t)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3011";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3011";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function l(){return"production"}function i(){return"development"===l()}t.d(r,{u:()=>d});let o={baseUrl:s(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:l(),isDevelopment:i(),isProduction:"production"===l(),port:process.env.PORT||"3011"};i()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",o.baseUrl),console.log("  API Base URL:",o.apiBaseUrl),console.log("  NextAuth URL:",o.nextAuthUrl),console.log("  Environment:",o.environment),console.log("  Port:",o.port));let n=a();class c{constructor(e=n){this.baseURL=e}async request(e,r={}){try{let t=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...r.headers},...r};console.log("API request:",{url:t,config:s});let a=await fetch(t,s),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let r=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&r.append(e,t.toString())});let t=r.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,r){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(r)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let r=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&r.append(e,t.toString())});let t=r.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let r=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&r.append(e,t.toString())});let t=r.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,r){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(r)})}async rejectTool(e,r){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(r)})}async getAdminStats(e){let r=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${r}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,r){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(r)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}}let d=new c},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59839:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var s=t(60687),a=t(43210),l=t.n(a),i=t(82136),o=t(12340),n=t(33823),c=t(11011),d=t(56976),u=t(28559),m=t(58869),p=t(62688);let h=(0,p.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),x=(0,p.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),f=(0,p.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),b=(0,p.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var g=t(99891);function y(){let{data:e,status:r,update:t}=(0,i.useSession)();(0,o.rd)();let[p,y]=(0,a.useState)(!1),[v,j]=(0,a.useState)(""),[w,N]=(0,a.useState)(""),[k,A]=(0,a.useState)(!1),[P,U]=(0,a.useState)({name:"",email:"",bio:"",website:"",location:""}),C=l().useRef(null),[T,_]=(0,a.useState)({emailNotifications:!0,toolApprovalNotifications:!0,weeklyDigest:!1,marketingEmails:!1}),[L,E]=(0,a.useState)({profileVisibility:"public",showEmail:!1,showSubmittedTools:!0}),S=async r=>{r.preventDefault(),y(!0),j(""),N("");try{let r=await d.u.updateProfile({name:P.name,bio:P.bio,website:P.website,location:P.location});r.success?(await t({...e,user:{...e?.user,name:P.name}}),N("个人资料已更新")):j(r.error||"更新失败，请重试")}catch(e){console.error("Profile update error:",e),j("更新失败，请重试")}finally{y(!1)}},R=async r=>{let s=r.target.files?.[0];if(s){if(!s.type.startsWith("image/"))return void j("请选择图片文件");if(s.size>5242880)return void j("图片文件大小不能超过5MB");A(!0),j("");try{let r=new FormData;r.append("avatar",s);let a=await fetch("/api/upload/avatar",{method:"POST",body:r}),l=await a.json();l.success?(await t({...e,user:{...e?.user,image:l.data.avatarUrl}}),N("头像更新成功")):j(l.error||"头像更新失败")}catch(e){console.error("Avatar upload error:",e),j("头像上传失败，请重试")}finally{A(!1),C.current&&(C.current.value="")}}},q=async()=>{if(e?.user?.image){A(!0),j("");try{let r=await d.u.updateProfile({avatar:""});r.success?(await t({...e,user:{...e?.user,image:null}}),N("头像已删除")):j(r.error||"头像删除失败")}catch(e){console.error("Avatar delete error:",e),j("头像删除失败，请重试")}finally{A(!1)}}},$=async e=>{e.preventDefault(),y(!0),j(""),N("");try{await new Promise(e=>setTimeout(e,1e3)),N("通知设置已更新")}catch(e){j("更新失败，请重试")}finally{y(!1)}},O=async e=>{e.preventDefault(),y(!0),j(""),N("");try{await new Promise(e=>setTimeout(e,1e3)),N("隐私设置已更新")}catch(e){j("更新失败，请重试")}finally{y(!1)}};return"loading"===r?(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsx)(n.A,{size:"lg",className:"py-20"})}):e?(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)(o.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(u.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"账户设置"}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:"管理您的个人资料和偏好设置"})]})]}),w&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-green-800",children:w})}),v&&(0,s.jsx)(c.default,{message:v,onClose:()=>j(""),className:"mb-6"}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(m.A,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"个人资料"})]}),(0,s.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"头像"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,s.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,s.jsx)("span",{className:"text-xl font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{C.current?.click()},disabled:k,className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[k?(0,s.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,s.jsx)(h,{className:"mr-2 h-4 w-4"}),k?"上传中...":"更换头像"]}),e.user?.image&&(0,s.jsxs)("button",{type:"button",onClick:q,disabled:k,className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsx)(x,{className:"mr-2 h-4 w-4"}),"删除"]})]})]}),(0,s.jsx)("input",{ref:C,type:"file",accept:"image/*",onChange:R,className:"hidden"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名"}),(0,s.jsx)("input",{type:"text",id:"name",value:P.name,onChange:e=>U(r=>({...r,name:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱"}),(0,s.jsx)("input",{type:"email",id:"email",value:P.email,onChange:e=>U(r=>({...r,email:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),(0,s.jsx)("textarea",{id:"bio",rows:3,value:P.bio,onChange:e=>U(r=>({...r,bio:e.target.value})),placeholder:"介绍一下您自己...",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:"个人网站"}),(0,s.jsx)("input",{type:"url",id:"website",value:P.website,onChange:e=>U(r=>({...r,website:e.target.value})),placeholder:"https://example.com",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-2",children:"所在地"}),(0,s.jsx)("input",{type:"text",id:"location",value:P.location,onChange:e=>U(r=>({...r,location:e.target.value})),placeholder:"城市, 国家",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)("button",{type:"submit",disabled:p,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[p?(0,s.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,s.jsx)(f,{className:"mr-2 h-5 w-5"}),"保存更改"]})})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(b,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"通知设置"})]}),(0,s.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"邮件通知"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"接收重要更新的邮件通知"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:T.emailNotifications,onChange:e=>_(r=>({...r,emailNotifications:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"工具审核通知"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"当您提交的工具审核状态变更时通知您"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:T.toolApprovalNotifications,onChange:e=>_(r=>({...r,toolApprovalNotifications:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"每周摘要"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"接收每周的新工具和热门内容摘要"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:T.weeklyDigest,onChange:e=>_(r=>({...r,weeklyDigest:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"营销邮件"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"接收产品更新和特别优惠信息"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:T.marketingEmails,onChange:e=>_(r=>({...r,marketingEmails:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,s.jsx)("div",{className:"flex justify-end pt-4",children:(0,s.jsxs)("button",{type:"submit",disabled:p,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[p?(0,s.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,s.jsx)(f,{className:"mr-2 h-5 w-5"}),"保存设置"]})})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(g.A,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"隐私设置"})]}),(0,s.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人资料可见性"}),(0,s.jsxs)("select",{value:L.profileVisibility,onChange:e=>E(r=>({...r,profileVisibility:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"public",children:"公开 - 任何人都可以查看"}),(0,s.jsx)("option",{value:"private",children:"私密 - 只有您可以查看"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"显示邮箱地址"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"在您的公开资料中显示邮箱地址"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:L.showEmail,onChange:e=>E(r=>({...r,showEmail:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"显示提交的工具"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"在您的公开资料中显示您提交的工具"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:L.showSubmittedTools,onChange:e=>E(r=>({...r,showSubmittedTools:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)("button",{type:"submit",disabled:p,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[p?(0,s.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,s.jsx)(f,{className:"mr-2 h-5 w-5"}),"保存设置"]})})]})]})]})]}):null}},62688:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),n=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:o("lucide",l),...!i&&!n(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(i)?i:[i]])),u=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...l},n)=>(0,s.createElement)(d,{ref:n,iconNode:r,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64994:(e,r,t)=>{Promise.resolve().then(t.bind(t,78857))},67182:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),a=t(48088),l=t(88170),i=t.n(l),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let c={children:["",{children:["[locale]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78857)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/settings/page",pathname:"/[locale]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78857:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},83146:(e,r,t)=>{Promise.resolve().then(t.bind(t,59839))},93613:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,4999,9658,6435,6699,1430,2585],()=>t(67182));module.exports=s})();