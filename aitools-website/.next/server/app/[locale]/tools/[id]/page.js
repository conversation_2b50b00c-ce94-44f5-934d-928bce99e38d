(()=>{var e={};e.id=6249,e.ids=[6249],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15985:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(60687),r=s(43210),n=s(82136),o=s(77618),i=s(23877);function l({toolId:e,initialComments:t,onLoginRequired:s}){let{data:l}=(0,n.useSession)(),[c,d]=(0,r.useState)(t),[m,u]=(0,r.useState)(""),[p,x]=(0,r.useState)(null),[h,g]=(0,r.useState)(""),[f,b]=(0,r.useState)(!1),j=(0,o.c3)("comments"),y=async()=>{try{let t=await fetch(`/api/tools/${e}/comments`);if(t.ok){let e=await t.json();e.success&&d(e.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}},v=async()=>{if(!l)return void s?.();if(m.trim()){b(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:m.trim()})});if(t.ok)(await t.json()).success&&(u(""),y());else{let e=await t.json();console.error("Comment submission failed:",e.message)}}catch(e){console.error("Comment submission error:",e)}finally{b(!1)}}},w=async()=>{if(!l)return void s?.();if(h.trim()&&p){b(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:h.trim(),parentId:p})});if(t.ok)(await t.json()).success&&(g(""),x(null),y());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{b(!1)}}},N=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/36e5);if(s<1)return j("just_now");{if(s<24)return j("hours_ago",{hours:s});let e=Math.floor(s/24);return e<7?j("days_ago",{days:e}):t.toLocaleDateString()}},_=(e,t=!1)=>(0,a.jsxs)("div",{className:`${t?"ml-8 border-l-2 border-gray-100 pl-4":""}`,children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:e.userId.image?(0,a.jsx)("img",{src:e.userId.image,alt:e.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.userId.name}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:N(e.createdAt)})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-700",children:e.content}),(0,a.jsx)("div",{className:"mt-2 flex items-center space-x-4",children:(0,a.jsxs)("button",{className:"text-xs text-gray-500 hover:text-blue-600 flex items-center space-x-1",onClick:()=>x(p===e._id?null:e._id),children:[(0,a.jsx)(i.w1Z,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:j("reply")})]})}),p===e._id&&(0,a.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,a.jsx)("textarea",{value:h,onChange:e=>g(e.target.value),placeholder:l?j("write_reply"):j("login_to_reply"),className:"w-full p-2 text-sm border border-gray-300 rounded resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:2,maxLength:500,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[h.length,"/500"]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{x(null),g("")},className:"px-3 py-1 text-xs text-gray-600 hover:text-gray-800",children:j("cancel")}),(0,a.jsx)("button",{onClick:w,disabled:f||!h.trim()||!l,className:"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?j("submitting"):j("submit_reply")})]})]})]})]})]}),e.replies&&e.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:e.replies.map(e=>_(e,!0))})]},e._id);return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:[j("comments")," (",c.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsx)("textarea",{value:m,onChange:e=>u(e.target.value),placeholder:l?j("write_comment"):j("login_to_comment"),className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[m.length,"/1000"]}),(0,a.jsx)("button",{onClick:v,disabled:f||!m.trim()||!l,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?j("submitting"):j("submit_comment")})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:0===c.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:j("no_comments")}):c.map(e=>_(e))})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24443:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(56037),r=s.n(a);let n=new a.Schema({toolId:{type:a.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},content:{type:String,required:[!0,"Comment content is required"],trim:!0,maxlength:[1e3,"Comment cannot exceed 1000 characters"],minlength:[1,"Comment cannot be empty"]},parentId:{type:a.Schema.Types.ObjectId,ref:"Comment",default:null},likes:{type:Number,default:0,min:0},isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({toolId:1,createdAt:-1}),n.index({userId:1}),n.index({parentId:1}),n.index({isActive:1}),n.virtual("replyCount",{ref:"Comment",localField:"_id",foreignField:"parentId",count:!0,match:{isActive:!0}}),n.statics.getToolComments=function(e){return this.find({toolId:e,isActive:!0}).populate("userId","name avatar").populate({path:"parentId",select:"content userId",populate:{path:"userId",select:"name"}}).sort({createdAt:-1})},n.methods.getReplies=function(){return r().model("Comment").find({parentId:this._id,isActive:!0}).populate("userId","name avatar").sort({createdAt:1})};let o=r().models.Comment||r().model("Comment",n)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32134:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,15985)),Promise.resolve().then(s.bind(s,7485)),Promise.resolve().then(s.bind(s,63701))},33873:e=>{"use strict";e.exports=require("path")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63701:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var a=s(60687),r=s(43210),n=s.n(r),o=s(48577);function i({children:e}){let[t,s]=(0,r.useState)(!1),i=()=>{s(!0)},l=n().Children.map(e,e=>n().isValidElement(e)?n().cloneElement(e,{onLoginRequired:i}):e);return(0,a.jsxs)(a.Fragment,{children:[l,(0,a.jsx)(o.A,{isOpen:t,onClose:()=>s(!1)})]})}},73916:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_,generateMetadata:()=>N});var a=s(37413);s(61120);var r=s(78878),n=s(39916),o=s(64348),i=s(21659),l=s(75745),c=s(24443),d=s(80555);async function m(e){try{await (0,l.A)();let t=await c.A.find({toolId:e,isActive:!0,parentId:null}).populate("userId","name email image").sort({createdAt:-1}).limit(10);return await Promise.all(t.map(async e=>{let t=await c.A.find({parentId:e._id,isActive:!0}).populate("userId","name email image").sort({createdAt:1});return{_id:e._id.toString(),content:e.content,userId:{_id:e.userId._id.toString(),name:e.userId.name,email:e.userId.email,image:e.userId.image},createdAt:e.createdAt.toISOString(),likes:e.likes||0,replies:t.map(e=>({_id:e._id.toString(),content:e.content,userId:{_id:e.userId._id.toString(),name:e.userId.name,email:e.userId.email,image:e.userId.image},createdAt:e.createdAt.toISOString(),likes:e.likes||0}))}}))}catch(e){return console.error("Error fetching comments:",e),[]}}async function u({toolId:e,onLoginRequired:t}){let s=await m(e);return(0,a.jsx)(d.default,{toolId:e,initialComments:s,onLoginRequired:t})}var p=s(93331),x=s(10806),h=s(79171),g=s(33488),f=s(94014),b=s(1215),j=s(85838);async function y(e,t){try{let s=await x.u.getTools({category:e,limit:4,status:"approved"});if(s.success&&s.data)return s.data.tools.filter(e=>e._id!==t);return[]}catch(e){return console.error("Error fetching related tools:",e),[]}}async function v({initialTool:e,toolId:t,locale:s}){let n=await (0,o.A)({locale:s,namespace:"tool_detail"}),l=await (0,o.A)({locale:s,namespace:"tags"}),c=await y(e.category,t);return(0,a.jsx)(p.default,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.logo?(0,a.jsx)("img",{src:e.logo,alt:`${e.name} logo`,className:"w-16 h-16 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:e.name}),e.tagline&&(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:e.tagline}),(0,a.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${(0,h.Ef)(e.pricing)}`,children:(0,h.mV)(e.pricing)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(i.default,{toolId:t,size:"lg"}),(0,a.jsxs)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),n("visit_website")]})]})]}),(0,a.jsxs)("div",{className:"prose max-w-none",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-3",children:n("description")}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed",children:e.description})]}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(f.A,{className:"mr-2 h-5 w-5"}),n("tags")]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,t)=>(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800",children:l(e)},t))})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(b.A,{className:"mr-2 h-5 w-5"}),(0,a.jsxs)("span",{children:[e.views||0," ",n("views")]})]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(j.A,{className:"mr-2 h-5 w-5"}),(0,a.jsxs)("span",{children:[e.likes||0," ",n("likes")]})]})]})})]}),(0,a.jsx)(u,{toolId:t})]}),(0,a.jsxs)("aside",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:n("tool_info")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:n("category")}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:e.category})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:n("pricing_model")}),(0,a.jsx)("span",{className:`px-2 py-1 rounded text-sm font-medium ${(0,h.Ef)(e.pricing)}`,children:(0,h.mV)(e.pricing)})]}),e.launchDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:n("launch_date")}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(e.launchDate).toLocaleDateString()})]})]})]}),c.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:n("related_tools")}),(0,a.jsx)("div",{className:"space-y-4",children:c.map(e=>(0,a.jsx)(r.N_,{href:`/tools/${e._id}`,className:"block p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,a.jsx)("img",{src:e.logo,alt:`${e.name} logo`,className:"w-10 h-10 rounded object-cover"}):(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.description})]})]})},e._id))})]})]})]})})}var w=s(54290);async function N({params:e}){try{let{id:t,locale:s}=await e,a=await (0,o.A)({locale:s,namespace:"tool_detail"}),r=await x.u.getTool(t);if(!r.success||!r.data){let e=await (0,o.A)({locale:s,namespace:"site"});return{title:`${a("not_found")} - ${e("title")}`,description:a("not_found_desc")}}let n=r.data,i=await (0,o.A)({locale:s,namespace:"site"}),l=`${n.name} - ${i("title")}`,c=n.description||`${n.name} is an excellent AI tool to boost your productivity.`,d=[n.name,...n.tags||[],"AI tools",n.category].join(", "),m=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",u=`/tools/${n._id}`,p=n.logo||"/og-tool-default.jpg";return{title:l,description:c,keywords:d,authors:[{name:i("title")}],robots:{index:!0,follow:!0},openGraph:{type:"article",locale:"zh"===s?"zh_CN":"en_US",url:`${m}${u}`,siteName:i("title"),title:l,description:c,images:[{url:p.startsWith("http")?p:`${m}${p}`,width:1200,height:630,alt:`${n.name} - ${i("title")}`}],publishedTime:n.launchDate?new Date(n.launchDate).toISOString():void 0,modifiedTime:n.updatedAt?new Date(n.updatedAt).toISOString():void 0},twitter:{card:"summary_large_image",title:l,description:c,images:[p.startsWith("http")?p:`${m}${p}`]},alternates:{canonical:`${m}${u}`}}}catch(s){let e=await (0,o.A)({locale:"zh",namespace:"site"}),t=await (0,o.A)({locale:"zh",namespace:"tool_detail"});return{title:`${t("page_title")} - ${e("title")}`,description:t("not_found_desc")}}}async function _({params:e}){try{let{id:t,locale:s}=await e,i=await (0,o.A)({locale:s,namespace:"tool_detail"}),l=await x.u.getTool(t);l.success&&l.data||(0,n.notFound)();let c=l.data,d=(0,w.sU)(c),m=(0,w.hC)([{name:i("breadcrumb_home"),url:"/"},{name:i("breadcrumb_tools"),url:"/tools"},{name:c.name,url:`/tools/${c._id}`}]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(d)}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(m)}}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6","aria-label":i("breadcrumb_aria_label"),children:[(0,a.jsx)(r.N_,{href:"/",className:"hover:text-blue-600",children:i("breadcrumb_home")}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(r.N_,{href:"/tools",className:"hover:text-blue-600",children:i("breadcrumb_tools")}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:c.name})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(r.N_,{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),i("back_to_tools")]})}),(0,a.jsx)(v,{initialTool:c,toolId:t,locale:s})]})]})}catch(e){console.error("Error loading tool:",e),(0,n.notFound)()}}},75745:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(56037),r=s.n(a);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let i=async function(){if(o.conn)return o.conn;o.promise||(o.promise=r().connect(n,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},79551:e=>{"use strict";e.exports=require("url")},80555:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx","default")},86442:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=s(65239),r=s(48088),n=s(88170),o=s.n(n),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c={children:["",{children:["[locale]",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73916)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/tools/[id]/page",pathname:"/[locale]/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91966:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,80555)),Promise.resolve().then(s.bind(s,21659)),Promise.resolve().then(s.bind(s,93331))},93331:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx","default")},94014:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(26373).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,6435,6699,1430,2585,3587],()=>s(86442));module.exports=a})();