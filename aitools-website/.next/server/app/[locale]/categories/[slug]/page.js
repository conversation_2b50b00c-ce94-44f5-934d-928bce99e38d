(()=>{var e={};e.id=14,e.ids=[14],e.modules={2879:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryToolsDisplayClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryToolsDisplayClient.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36262:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b,generateMetadata:()=>h});var r=s(37413);s(61120);var a=s(39916),l=s(78878),o=s(64348),i=s(56333),n=s(51465),c=s(2879);async function d({categoryInfo:e,tools:t,error:s,locale:a}){let d=await (0,o.A)({locale:a,namespace:"category_page"});return s?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.default,{message:s})}):e?(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,r.jsx)(l.N_,{href:"/",className:"hover:text-blue-600",children:d("breadcrumb_home")}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(l.N_,{href:"/categories",className:"hover:text-blue-600",children:d("breadcrumb_categories")}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900",children:e.name})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(l.N_,{href:"/categories",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),d("back_to_categories")]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-3xl",style:{backgroundColor:e.color},children:(0,r.jsx)("span",{className:"text-white",children:e.icon})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:d("tools_count",{count:e.toolCount})})]})]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]}),(0,r.jsx)(c.default,{tools:t}),(0,r.jsxs)("section",{className:"mt-16 bg-gray-50 rounded-lg p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:d("related_categories")}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)(l.N_,{href:"/categories/text-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:d("text_generation")})]}),(0,r.jsxs)(l.N_,{href:"/categories/image-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA8"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:d("image_generation")})]}),(0,r.jsxs)(l.N_,{href:"/categories/code-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:d("code_generation")})]}),(0,r.jsxs)(l.N_,{href:"/categories/audio-processing",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:d("audio_processing")})]})]})]})]}):(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:d("not_found_title")}),(0,r.jsx)("p",{className:"text-gray-600",children:d("not_found_desc")}),(0,r.jsx)(l.N_,{href:"/categories",className:"text-blue-600 hover:text-blue-700 mt-4 inline-block",children:d("back_to_categories")})]})})}var m=s(10806),p=s(60366),u=s(54290);let x=async(e,t,s)=>{let r=await (0,p.PZ)(e,s);return r?{_id:e,slug:e,name:r.name,description:r.description,icon:r.icon,color:r.color,toolCount:t}:{_id:e,slug:e,name:e.replace("-"," ").replace(/\b\w/g,e=>e.toUpperCase()),description:`AI tools in the ${e} category.`,icon:"\uD83E\uDD16",color:"#6B7280",toolCount:t}};async function h({params:e}){try{let t=await e,{categoryInfo:s,tools:r}=await g(t.slug,t.locale);if(!s)return{title:"分类不存在 - AI工具导航",description:"您访问的AI工具分类不存在。"};let a=`${s.name} AI工具 - AI工具导航`,l=`发现最好的${s.name} AI工具。${s.description}，共${r.length}个精选工具。`,o=`${s.name},${s.name}AI工具,人工智能,AI应用,机器学习,深度学习`,i=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",n=`/categories/${s.slug}`;return{title:a,description:l,keywords:o,authors:[{name:"AI工具导航团队"}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh_CN",url:`${i}${n}`,siteName:"AI工具导航",title:a,description:l,images:[{url:`/og-category-${s.slug}.jpg`,width:1200,height:630,alt:`${s.name} AI工具`}]},twitter:{card:"summary_large_image",title:a,description:l,images:[`/og-category-${s.slug}.jpg`]},alternates:{canonical:`${i}${n}`}}}catch(e){return{title:"AI工具分类 - AI工具导航",description:"浏览AI工具分类，发现适合您需求的人工智能工具。"}}}async function g(e,t){try{let s=await m.u.getTools({category:e,status:"published",limit:100});if(!s.success||!s.data)return{categoryInfo:null,tools:[],error:s.error||"获取分类数据失败"};{let r=s.data.tools;return{categoryInfo:await x(e,r.length,t),tools:r,error:null}}}catch(e){return console.error("Failed to fetch category data:",e),{categoryInfo:null,tools:[],error:"获取分类数据失败，请稍后重试"}}}async function b({params:e}){let t=await e,{categoryInfo:s,tools:l,error:o}=await g(t.slug,t.locale);s||(0,a.notFound)();let i=l.length>0?(0,u.eb)(l,s.name):null,n=(0,u.hC)([{name:"首页",url:"/"},{name:"AI工具分类",url:"/categories"},{name:s.name,url:`/categories/${s.slug}`}]),c={"@context":"https://schema.org","@type":"CollectionPage",name:`${s.name} AI工具`,description:s.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/categories/${s.slug}`,mainEntity:{"@type":"ItemList",name:`${s.name} AI工具列表`,numberOfItems:l.length,itemListElement:l.map((e,t)=>({"@type":"ListItem",position:t+1,item:{"@type":"SoftwareApplication",name:e.name,description:e.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/tools/${e._id}`,applicationCategory:s.name,image:e.logo}}))}};return(0,r.jsxs)(r.Fragment,{children:[i&&(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(i)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(n)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(c)}}),(0,r.jsx)(d,{categoryInfo:s,tools:l,error:o,locale:t.locale})]})}},51465:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},59031:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.bind(s,82724)),Promise.resolve().then(s.bind(s,11011))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64607:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.bind(s,2879)),Promise.resolve().then(s.bind(s,56333))},78272:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},80872:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),o=s.n(l),i=s(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c={children:["",{children:["[locale]",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36262)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/categories/[slug]/page",pathname:"/[locale]/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},82724:(e,t,s)=>{"use strict";s.d(t,{default:()=>p});var r=s(60687),a=s(43210),l=s(77618),o=s(80462),i=s(78272),n=s(6943),c=s(25366);function d({tools:e,onFilteredToolsChange:t}){let[s,d]=(0,a.useState)(""),[m,p]=(0,a.useState)(""),[u,x]=(0,a.useState)("popular"),[h,g]=(0,a.useState)("grid"),[b,y]=(0,a.useState)(!1),f=(0,l.c3)("category_page"),v=[{value:"",label:f("pricing_all")},{value:"free",label:f("pricing_free")},{value:"freemium",label:f("pricing_freemium")},{value:"paid",label:f("pricing_paid")}],j=[{value:"popular",label:f("sort_popular")},{value:"newest",label:f("sort_newest")},{value:"name",label:f("sort_name")},{value:"views",label:f("sort_views")}];return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)("input",{type:"text",placeholder:f("search_placeholder"),value:s,onChange:e=>d(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(o.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"md:hidden mb-4",children:(0,r.jsxs)("button",{onClick:()=>y(!b),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),f("filter_options"),(0,r.jsx)(i.A,{className:`ml-2 h-4 w-4 transform ${b?"rotate-180":""}`})]})}),(0,r.jsxs)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-4 ${b?"block":"hidden md:grid"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f("pricing")}),(0,r.jsx)("select",{value:m,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:v.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f("sort")}),(0,r.jsx)("select",{value:u,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:j.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f("view")}),(0,r.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,r.jsx)("button",{onClick:()=>g("grid"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${"grid"===h?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(n.A,{className:"h-4 w-4 mx-auto"})}),(0,r.jsx)("button",{onClick:()=>g("list"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${"list"===h?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(c.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]})})}var m=s(39581);function p({tools:e}){let[t,s]=(0,a.useState)(e),[i,n]=(0,a.useState)("grid"),[c,p]=(0,a.useState)(""),u=(0,l.c3)("category_page"),x=(0,a.useCallback)((e,t,r)=>{s(e),n(t),p(r)},[]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d,{tools:e,onFilteredToolsChange:x}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-600",children:[u("results_count",{count:t.length}),c&&` ${u("search_for",{term:c})}`]})}),t.length>0?(0,r.jsx)("div",{className:"grid"===i?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:t.map(e=>(0,r.jsx)(m.A,{tool:e},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(o.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:u("no_results_title")}),(0,r.jsx)("p",{className:"text-gray-600",children:u("no_results_desc")})]})]})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,1430,6533,2585,9581,7702],()=>s(80872));module.exports=r})();