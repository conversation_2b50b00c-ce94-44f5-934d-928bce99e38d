(()=>{var e={};e.id=6577,e.ids=[6577],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var s=r(36344),a=r(65752),i=r(13581),n=r(75745),o=r(17063);let l={...!1,providers:[(0,s.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,i.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[s,a]=r.split(":");if(s!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,n.A)();try{let s=await o.A.findOne({email:e.email});return s?s.lastLoginAt=new Date:s=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await s.save(),t&&"email-code"!==t.provider&&(s.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await s.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),a=r.n(s);let i=new s.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new s.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[i],submittedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:s.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},n.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=a().models.User||a().model("User",n)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21039:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var s=r(60687),a=r(43210),i=r(12340),n=r(39010),o=r(46299),l=r(77618);let c=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var d=r(85778),u=r(94865);function m({onSuccess:e,amount:t}){let r=(0,o.t2)(),n=(0,o.HH)(),[m,p]=(0,a.useState)(!1),[h,x]=(0,a.useState)(""),y=(0,i.a8)(),f=(0,l.c3)("payment"),g=y?.startsWith("/en")?"en":"zh",v=async t=>{if(t.preventDefault(),r&&n){p(!0),x("");try{let{error:t}=await r.confirmPayment({elements:n,confirmParams:{return_url:`${window.location.origin}/${g}/submit/success`},redirect:"if_required"});t?"card_error"===t.type||"validation_error"===t.type?x(t.message||f("payment_failed")):x(f("payment_error")):e()}catch(e){x(f("payment_processing_failed"))}finally{p(!1)}}};return(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:f("payment_method")}),(0,s.jsx)(o.He,{options:{layout:"tabs",defaultValues:{billingDetails:{address:{country:"CN"}}}}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:f("billing_address")}),(0,s.jsx)(o.H1,{options:{mode:"billing",defaultValues:{address:{country:"CN"}}}})]}),h&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:h})}),(0,s.jsx)("button",{type:"submit",disabled:!r||!n||m,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors",children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c,{className:"h-5 w-5 mr-2 animate-spin"}),f("processing_payment")]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2"}),f("pay_now",{amount:(0,u.tF)(t)})]})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-500 text-xs",children:f("security_notice")})})]})}var p=r(93613),h=r(5336),x=r(99891),y=r(56976);let f=(0,n.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function g({order:e,orderId:t}){let r=(0,i.rd)(),[n,c]=(0,a.useState)(""),[g,v]=(0,a.useState)(""),[b,_]=(0,a.useState)(!0),A=(0,l.c3)("checkout"),w=async()=>{try{let e=await y.u.processOrderPayment(t,{paymentMethod:"stripe"});e.success?console.log("订单状态更新成功:",e.data):console.warn("订单状态更新失败:",e.error)}catch(e){console.error("调用订单支付接口失败:",e)}r.push(`/submit/launch-date-success?toolId=${e.toolId}&paid=true`)};return b?(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:A("creating_payment_session")})]})}):g?(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(p.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:A("payment_error_title")}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:g}),(0,s.jsx)("button",{onClick:()=>r.push("/submit"),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:A("back_to_submit")})]})}):(0,s.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:A("page_title")}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:A("page_subtitle")})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:A("order_details")}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:A("service_type")}),(0,s.jsx)("span",{className:"font-medium",children:A("tool_priority_launch")})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:A("tool_name")}),(0,s.jsx)("span",{className:"font-medium",children:e.tool?.name||A("loading_placeholder")})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:A("launch_date")}),(0,s.jsx)("span",{className:"font-medium",children:e.selectedLaunchDate?new Date(e.selectedLaunchDate).toLocaleDateString():A("loading_placeholder")})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:A("order_number")}),(0,s.jsx)("span",{className:"font-medium text-sm",children:e._id})]}),(0,s.jsx)("hr",{className:"my-4"}),(0,s.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,s.jsx)("span",{children:A("total")}),(0,s.jsx)("span",{className:"text-blue-600",children:(0,u.tF)(e.amount)})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:A("priority_service_title")}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),A("feature_any_date")]}),(0,s.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),A("feature_priority_review")]}),(0,s.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),A("feature_homepage_featured")]}),(0,s.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),A("feature_dedicated_support")]})]})]}),(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-2 text-green-500"}),(0,s.jsx)("span",{className:"text-sm",children:A("security_notice")})]})}),n&&(0,s.jsx)(o.S8,{stripe:f,options:{clientSecret:n,appearance:{theme:"stripe",variables:{colorPrimary:"#2563eb"}}},children:(0,s.jsx)(m,{onSuccess:w,amount:e.amount})}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-4 text-center",children:A("terms_notice")})]})}},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",i),...!n&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},l)=>(0,s.createElement)(d,{ref:l,iconNode:t,className:o(`lucide-${a(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31098:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),a=r.n(s);let i=new s.Schema({userId:{type:s.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:s.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});i.index({userId:1,createdAt:-1}),i.index({toolId:1}),i.index({status:1}),i.index({paymentIntentId:1}),i.index({paymentSessionId:1}),i.index({stripePaymentIntentId:1}),i.index({stripeCustomerId:1}),i.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),i.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),i.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},i.methods.markAsFailed=function(){return this.status="failed",this.save()},i.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},i.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let n=a().models.Order||a().model("Order",i)},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},56976:(e,t,r)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3011";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3011";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function i(){return"production"}function n(){return"development"===i()}r.d(t,{u:()=>d});let o={baseUrl:s(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:i(),isDevelopment:n(),isProduction:"production"===i(),port:process.env.PORT||"3011"};n()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",o.baseUrl),console.log("  API Base URL:",o.apiBaseUrl),console.log("  NextAuth URL:",o.nextAuthUrl),console.log("  Environment:",o.environment),console.log("  Port:",o.port));let l=a();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t};console.log("API request:",{url:r,config:s});let a=await fetch(r,s),i=await a.json();if(!a.ok)throw Error(i.error||`HTTP error! status: ${a.status}`);return i}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}}let d=new c},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",i),...!n&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},l)=>(0,s.createElement)(d,{ref:l,iconNode:t,className:o(`lucide-${a(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63126:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["payment",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92413)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/payment/checkout/page",pathname:"/[locale]/payment/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73632:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,21039))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),a=r.n(s);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(i,{bufferCommands:!1}));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84731:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx","default")},85778:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86784:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,84731))},92413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(37413),a=r(78878),i=r(35426),n=r(12909),o=r(84731);let l=(0,r(26373).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var c=r(75745),d=r(31098),u=r(17063),m=r(56037),p=r.n(m),h=r(64348);async function x(e,t){try{if(await (0,c.A)(),!p().Types.ObjectId.isValid(e))return null;let r=await u.A.findOne({email:t});if(!r)return null;let s=await d.A.findById(e).populate("toolId","name description");if(!s||s.userId.toString()!==r._id.toString())return null;return{_id:s._id.toString(),type:s.type,amount:s.amount,currency:s.currency,status:s.status,description:s.description,selectedLaunchDate:s.selectedLaunchDate?s.selectedLaunchDate.toISOString():null,createdAt:s.createdAt?s.createdAt.toISOString():null,paidAt:s.paidAt?s.paidAt.toISOString():null,tool:s.toolId?{_id:s.toolId._id.toString(),name:s.toolId.name,description:s.toolId.description}:null,toolId:s.toolId?s.toolId._id.toString():null}}catch(e){return console.error("Failed to fetch order:",e),null}}async function y({searchParams:e,params:t}){let r=await (0,i.getServerSession)(n.N),{orderId:c}=await e,{locale:d}=await t,u=await (0,h.A)({locale:d,namespace:"checkout"});if(r?.user?.email||(0,a.V2)({href:"/",locale:d}),!c)return(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(l,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:u("order_not_found")}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:u("order_not_found_desc")})]})});let m=await x(c,r?.user?.email);return m?("completed"===m.status&&(0,a.V2)({href:`/submit/success?toolId=${m.toolId}`,locale:d}),"pending"!==m.status)?(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(l,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:u("order_status_error")}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:u("order_status_error_desc")})]})}):(0,s.jsx)(o.default,{order:m,orderId:c}):(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(l,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:u("order_not_found")}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:u("order_deleted_desc")})]})})}},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},94865:(e,t,r)=>{"use strict";r.d(t,{$g:()=>d,Ef:()=>l,S9:()=>u,Y$:()=>o,kX:()=>s,mV:()=>c,mp:()=>p,sT:()=>m,tF:()=>h,v4:()=>n,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",promotion:{enabled:!0,description:"限时优惠 - 前100个付费用户",discountPercent:50,remainingSlots:85},features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],o=[{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],l=e=>{switch(e){case i.FREE.value:return i.FREE.color;case i.FREEMIUM.value:return i.FREEMIUM.color;case i.PAID.value:return i.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case i.FREE.value:return i.FREE.label;case i.FREEMIUM.value:return i.FREEMIUM.label;case i.PAID.value:return i.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,m=()=>s.PRIORITY_LAUNCH.promotion,p=()=>{let e=s.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},h=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,1430,3136,7580,2585],()=>r(63126));module.exports=s})();