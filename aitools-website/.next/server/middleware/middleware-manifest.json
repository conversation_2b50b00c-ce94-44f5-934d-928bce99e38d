{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__abf7a29f._.js", "server/edge/chunks/edge-wrapper_80fb5899.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "a2724c9ea50db020766e4206a5c67f22", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6319b0912346bc09885cca27834836793608bc89ccb82aafe22fed8eebbd0c94", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "491be662901974386ab086c96961198f45b09a47345d503e7d561deceaf91bf7"}}}, "instrumentation": null, "functions": {}}