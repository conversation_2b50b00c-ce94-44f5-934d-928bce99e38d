(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2797],{309:(e,s,l)=>{Promise.resolve().then(l.bind(l,6160)),Promise.resolve().then(l.bind(l,6096)),Promise.resolve().then(l.bind(l,3391))},2765:(e,s,l)=>{"use strict";l.d(s,{J2:()=>t,VP:()=>a});var r=l(7652);let o=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];function t(){let e=(0,r.c3)("categories"),s=(function(){let e=(0,r.c3)("categories");return o.map(s=>({slug:s.slug,name:e("category_names.".concat(s.slug)),description:e("category_descriptions.".concat(s.slug)),icon:s.icon,color:s.color}))})().map(e=>({value:e.slug,label:e.name}));return[{value:"",label:e("all_categories")},...s]}function a(e){return(0,r.c3)("categories")("category_names.".concat(e))||e}async function n(e){let s=await getTranslations({locale:e||"en",namespace:"categories"});return o.map(e=>({slug:e.slug,name:s("category_names.".concat(e.slug)),description:s("category_descriptions.".concat(e.slug)),icon:e.icon,color:e.color}))}o.map(e=>e.slug),o.reduce((e,s)=>(e[s.slug]=s,e),{})},3391:(e,s,l)=>{"use strict";l.d(s,{default:()=>h});var r=l(5155),o=l(2115),t=l(5695),a=l(2388),n=l(9783),c=l(2765),i=l(7550),d=l(7924),u=l(6932),g=l(1976),m=l(617);function x(e){let{slug:s}=e,l=(0,c.VP)(s);return(0,r.jsx)(r.Fragment,{children:l})}function h(e){let{initialTools:s}=e,l=(0,t.useParams)(),c=(null==l?void 0:l.locale)||"zh",[h,p]=(0,o.useState)(s),[f,b]=(0,o.useState)(s),[v,j]=(0,o.useState)(""),[N,y]=(0,o.useState)(""),[w,C]=(0,o.useState)("all");(0,o.useEffect)(()=>{_()},[h,N,w]);let _=()=>{let e=h;N&&(e=e.filter(e=>e.name.toLowerCase().includes(N.toLowerCase())||e.description.toLowerCase().includes(N.toLowerCase()))),"all"!==w&&(e=e.filter(e=>e.category===w)),b(e)},A=async e=>{try{p(s=>s.filter(s=>s._id!==e)),j("")}catch(e){console.error("Error unliking tool:",e),j("取消收藏失败，请重试")}},F=Array.from(new Set(h.map(e=>e.category))),k="en"===c?{title:"My Favorites",subtitle:"Your favorite AI tools (".concat(h.length,")"),searchPlaceholder:"Search favorite tools...",allCategories:"All Categories",noToolsFound:"No matching tools found",noToolsYet:"No favorite tools yet",adjustFilters:"Try adjusting your search criteria or filters",startExploring:"Start exploring and favorite your preferred AI tools!",browseTools:"Browse Tools"}:{title:"我的收藏",subtitle:"您收藏的AI工具 (".concat(h.length,")"),searchPlaceholder:"搜索收藏的工具...",allCategories:"所有分类",noToolsFound:"没有找到匹配的工具",noToolsYet:"还没有收藏任何工具",adjustFilters:"尝试调整搜索条件或筛选器",startExploring:"开始探索并收藏您喜欢的AI工具吧！",browseTools:"浏览工具"};return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(a.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(i.A,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:k.title})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:k.subtitle})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:k.searchPlaceholder,value:N,onChange:e=>y(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsx)("div",{className:"sm:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("select",{value:w,onChange:e=>C(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"all",children:k.allCategories}),F.map(e=>(0,r.jsx)("option",{value:e,children:(0,r.jsx)(x,{slug:e})},e))]})]})})]})}),v&&(0,r.jsx)(n.default,{message:v,onClose:()=>j(""),className:"mb-6"}),f.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>(0,r.jsx)(m.A,{tool:e,onUnlike:A,isInLikedPage:!0},e._id))}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(g.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:N||"all"!==w?k.noToolsFound:k.noToolsYet}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:N||"all"!==w?k.adjustFilters:k.startExploring}),!N&&"all"===w&&(0,r.jsx)(a.N_,{href:"/tools",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:k.browseTools})]})]})}},9783:(e,s,l)=>{"use strict";l.d(s,{default:()=>a});var r=l(5155),o=l(5339),t=l(4416);function a(e){let{message:s,onClose:l,className:a=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(a),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:s})}),l&&(0,r.jsx)("button",{onClick:l,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(t.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,3385,6160,2108,8003,2571,617,8441,1684,7358],()=>s(309)),_N_E=e.O()}]);