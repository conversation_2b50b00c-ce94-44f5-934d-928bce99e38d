(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8450],{347:()=>{},848:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var l=r(5155),s=r(2108);function a(e){let{children:t}=e;return(0,l.jsx)(s.<PERSON>,{children:t})}},981:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var l=r(5695),s=r(2115),a=r.t(s,2),n=r(3385),i=a["use".trim()],o=r(3225),c=r(6160),d=r(469),u=r(5155),m=r(8986);function h(e){let{Link:t,config:r,getPathname:a,...h}=function(e,t){var r,a,n;let m={...r=t||{},localePrefix:"object"==typeof(n=r.localePrefix)?n:{mode:n||"always"},localeCookie:!!((a=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof a&&a},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},h=m.pathnames,x=(0,s.forwardRef)(function({href:t,locale:r,...l},s){let a,n;"object"==typeof t?(a=t.pathname,n=t.params):a=t;let d=(0,o._x)(t),x=e(),b=(0,o.yL)(x)?i(x):x,p=d?f({locale:r||b,href:null==h?a:{pathname:a,params:n},forcePrefix:null!=r||void 0}):a;return(0,u.jsx)(c.default,{ref:s,href:"object"==typeof t?{...t,pathname:p}:p,locale:r,localeCookie:m.localeCookie,...l})});function f(e){let t,{forcePrefix:r,href:l,locale:s}=e;return null==h?"object"==typeof l?(t=l.pathname,l.query&&(t+=(0,d.Zn)(l.query))):t=l:t=(0,d.FP)({locale:s,...(0,d.TK)(l),pathnames:m.pathnames}),(0,d.x3)(t,s,m,r)}function b(e){return function(t,...r){return e(f(t),...r)}}return{config:m,Link:x,redirect:b(l.redirect),permanentRedirect:b(l.permanentRedirect),getPathname:f}}(n.Ym,e);return{...h,Link:t,usePathname:function(){let e=function(e){let t=(0,l.usePathname)(),r=(0,n.Ym)();return(0,s.useMemo)(()=>{if(!t)return t;let l=t,s=(0,o.XP)(r,e.localePrefix);if((0,o.wO)(s,t))l=(0,o.MY)(t,s);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,o.bL)(r);(0,o.wO)(e,t)&&(l=(0,o.MY)(t,e))}return l},[e.localePrefix,r,t])}(r),t=(0,n.Ym)();return(0,s.useMemo)(()=>e&&r.pathnames?(0,d.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,l.useRouter)(),t=(0,n.Ym)(),i=(0,l.usePathname)();return(0,s.useMemo)(()=>{function l(e){return function(l,s){let{locale:n,...o}=s||{},c=[a({href:l,locale:n||t})];Object.keys(o).length>0&&c.push(o),e(...c),(0,m.A)(r.localeCookie,i,t,n)}}return{...e,push:l(e.push),replace:l(e.replace),prefetch:l(e.prefetch)}},[t,i,e])},getPathname:a}}},1682:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var l=r(5155),s=r(2388),a=r(7652),n=r(9911);function i(e){let{locale:t,className:r=""}=e,i=(0,s.rd)(),o=(0,a.c3)("navigation");return(0,l.jsx)("form",{onSubmit:e=>{e.preventDefault();let t=new FormData(e.currentTarget).get("search");t.trim()&&i.push("/search?q=".concat(encodeURIComponent(t.trim())))},className:r,children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,l.jsx)(n.KSO,{className:"text-gray-400"})}),(0,l.jsx)("input",{name:"search",type:"text",placeholder:o("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}},2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>n,a8:()=>o,rd:()=>c});var l=r(9984),s=r(981);let a=(0,l.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:i,usePathname:o,useRouter:c}=(0,s.A)(a)},3676:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var l=r(5155),s=r(2115),a=r(2388),n=r(9911),i=r(1682);let o=e=>{let{children:t,href:r,locale:s}=e;return(0,l.jsx)(a.N_,{href:r,locale:s,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:t})};function c(e){let{links:t,locale:r}=e,[a,c]=(0,s.useState)(!1);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>c(!a),"aria-label":"Open Menu",children:a?(0,l.jsx)(n.QCr,{}):(0,l.jsx)(n.OXb,{})}),a&&(0,l.jsx)("div",{className:"md:hidden pb-4",children:(0,l.jsxs)("nav",{className:"space-y-4",children:[t.map(e=>(0,l.jsx)(o,{href:e.href,locale:r,children:e.name},e.name)),(0,l.jsx)("div",{className:"pt-4",children:(0,l.jsx)(i.default,{locale:r})})]})})]})}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var l=r(2115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=l.createContext&&l.createContext(s),n=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&(e[l]=r[l])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,l)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var l,s,a;l=e,s=t,a=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var l=r.call(e,t||"default");if("object"!=typeof l)return l;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in l?Object.defineProperty(l,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[s]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>l.createElement(u,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>l.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:s,size:a,title:o}=e,d=function(e,t){if(null==e)return{};var r,l,s=function(e,t){if(null==e)return{};var r={};for(var l in e)if(Object.prototype.hasOwnProperty.call(e,l)){if(t.indexOf(l)>=0)continue;r[l]=e[l]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(l=0;l<a.length;l++)r=a[l],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,n),u=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),l.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&l.createElement("title",null,o),e.children)};return void 0!==a?l.createElement(a.Consumer,null,e=>t(e)):t(s)}},4556:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},5257:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var l=r(5155),s=r(2115),a=r(5695),n=r(9911);let i=["en","zh"],o={zh:"中文",en:"English"};function c(e){let{currentLocale:t}=e,[r,c]=(0,s.useState)(!1),d=(0,a.useRouter)(),u=(0,a.usePathname)(),m=e=>{var t;let r=(null==u||null==(t=u.split("/"))?void 0:t.filter(Boolean))||[];r.length>0&&i.includes(r[0])&&r.shift();let l="/".concat(e).concat(r.length>0?"/"+r.join("/"):"");d.replace(l),c(!1)};return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("button",{onClick:()=>c(!r),className:"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors",children:[(0,l.jsx)(n.f35,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:o[t]})]}),r&&(0,l.jsx)("div",{className:"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:i.map(e=>(0,l.jsx)("button",{onClick:()=>m(e),className:"block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ".concat(e===t?"bg-blue-50 text-blue-600":"text-gray-700"),children:o[e]},e))})]})}},6063:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var l=r(5155),s=r(2115),a=r(2108),n=r(2388),i=r(7652),o=r(3385),c=r(9911);function d(e){let{isOpen:t,onClose:r}=e,[d,u]=(0,s.useState)("method"),[m,h]=(0,s.useState)(""),[x,f]=(0,s.useState)(""),[b,p]=(0,s.useState)(!1),[g,v]=(0,s.useState)("");(0,n.a8)();let y=(0,i.c3)("auth");(0,o.Ym)();let j=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",r=document.createElement("div");r.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),r.textContent=e,document.body.appendChild(r),setTimeout(()=>document.body.removeChild(r),3e3)},N=()=>{u("method"),h(""),f(""),v(""),r()},k=async e=>{try{p(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){j(y("login_failed"),"error")}finally{p(!1)}},w=async()=>{if(!m)return void v(y("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m))return void v(y("email_invalid"));v(""),p(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m})}),t=await e.json();t.success?(f(t.token),u("code"),j(y("verification_sent"))):j(t.error||y("send_failed"),"error")}catch(e){j(y("network_error"),"error")}finally{p(!1)}},P=async e=>{if(6===e.length){p(!0);try{let t=await (0,a.signIn)("email-code",{email:m,code:e,token:x,redirect:!1});(null==t?void 0:t.ok)?(j(y("login_success")),N()):j((null==t?void 0:t.error)||y("verification_error"),"error")}catch(e){j(y("network_error"),"error")}finally{p(!1)}}},_=(e,t)=>{if(t.length>1)return;let r=document.querySelectorAll(".code-input");if(r[e].value=t,t&&e<5){var l;null==(l=r[e+1])||l.focus()}let s=Array.from(r).map(e=>e.value).join("");6===s.length&&P(s)};return t?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===d&&y("login_title"),"email"===d&&y("email_login_title"),"code"===d&&y("verification_title")]}),(0,l.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(c.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:y("choose_method")}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>k("google"),disabled:b,children:[(0,l.jsx)(c.DSS,{}),y("google_login")]})}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:y("or")})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>u("email"),children:[(0,l.jsx)(c.maD,{}),y("email_login")]})]}),"email"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:y("email_instruction")}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("email_address")}),(0,l.jsx)("input",{type:"email",value:m,onChange:e=>h(e.target.value),placeholder:y("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&w(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:w,disabled:b,children:b?y("sending"):y("send_code")}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("method"),children:y("back")})]})]}),"code"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:y("verification_instruction",{email:m})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("verification_code")}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:t=>_(e,t.target.value),disabled:b,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("email"),children:y("resend_code")}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("method"),children:y("back")})]})]})]})]})]}):null}},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var l=r(3385),s=r(5155);function a(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,s.jsx)(l.Dk,{locale:t,...r})}},6214:(e,t,r)=>{"use strict";r.d(t,{LikeProvider:()=>o,X:()=>c});var l=r(5155),s=r(2115),a=r(2108);let n={liked:!1,likes:0,loading:!1},i=(0,s.createContext)(null);function o(e){let{children:t}=e,{data:r}=(0,a.useSession)(),[o,c]=(0,s.useState)({}),d=(0,s.useCallback)(e=>o[e]||n,[o]),u=(0,s.useCallback)(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];c(l=>l[e]?l:{...l,[e]:{liked:r,likes:t,loading:!1}})},[]),m=(0,s.useCallback)(async e=>{if(r)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let r=await t.json();r.success&&c(t=>({...t,[e]:{liked:r.data.liked,likes:r.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[r]),h=(0,s.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!r)return!1;c(t=>({...t,[e]:{...t[e]||n,loading:!0}}));try{let r=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(r.ok){let t=await r.json();if(t.success)return c(r=>({...r,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return c(t=>({...t,[e]:{...t[e]||n,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),c(t=>({...t,[e]:{...t[e]||n,loading:!1}})),!1}},[r]);return(0,s.useEffect)(()=>{r?Object.keys(o).forEach(e=>{m(e)}):c(e=>{let t={};return Object.keys(e).forEach(r=>{t[r]={...e[r],liked:!1,loading:!1}}),t})},[r]),(0,l.jsx)(i.Provider,{value:{toolStates:o,toggleLike:h,getToolState:d,initializeToolState:u,refreshToolState:m},children:t})}function c(){let e=(0,s.useContext)(i);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},6396:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},6847:(e,t,r)=>{Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.t.bind(r,6396,23)),Promise.resolve().then(r.t.bind(r,4556,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,7580)),Promise.resolve().then(r.bind(r,5257)),Promise.resolve().then(r.bind(r,3676)),Promise.resolve().then(r.bind(r,1682)),Promise.resolve().then(r.bind(r,848)),Promise.resolve().then(r.bind(r,6214))},7580:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var l=r(5155),s=r(2115),a=r(2108),n=r(9911),i=r(2388),o=r(7652),c=r(6063);let d=e=>{let{children:t,href:r,locale:s}=e;return(0,l.jsx)(i.N_,{href:r,locale:s,className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:t})};function u(e){var t,r,u,m,h,x,f,b,p,g,v;let{locale:y}=e,{data:j,status:N}=(0,a.useSession)();(0,i.rd)();let[k,w]=(0,s.useState)(!1),[P,_]=(0,s.useState)(!1),O=(0,o.c3)("common"),C=async()=>{await (0,a.signOut)({callbackUrl:"/"})};return"loading"===N?(0,l.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:O("loading")}):j?(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-400 transition-colors",onClick:()=>_(!P),children:[(0,l.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(t=j.user)?void 0:t.image)?(0,l.jsx)("img",{src:j.user.image,alt:j.user.name||"",className:"w-full h-full object-cover"}):(0,l.jsx)("span",{className:"text-sm font-medium text-gray-600",children:(null==(u=j.user)||null==(r=u.name)?void 0:r.charAt(0))||"U"})}),(0,l.jsx)("span",{className:"text-sm hidden md:block",children:null==(m=j.user)?void 0:m.name}),(0,l.jsx)(n.Vr3,{className:"text-xs transition-transform ".concat(P?"rotate-180":"")})]}),P&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>_(!1)}),(0,l.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",onClick:()=>_(!P),children:[(0,l.jsx)("div",{className:"p-4 border-b",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(h=j.user)?void 0:h.image)?(0,l.jsx)("img",{src:j.user.image,alt:j.user.name||"",className:"w-full h-full object-cover"}):(0,l.jsx)("span",{className:"text-lg font-medium text-gray-600",children:(null==(f=j.user)||null==(x=f.name)?void 0:x.charAt(0))||"U"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-sm",children:null==(b=j.user)?void 0:b.name}),(0,l.jsx)("p",{className:"text-gray-500 text-xs",children:null==(p=j.user)?void 0:p.email}),(null==(g=j.user)?void 0:g.role)==="admin"&&(0,l.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:O("admin")})]})]})}),(0,l.jsxs)("div",{className:"py-2",children:[(0,l.jsxs)(d,{href:"/profile",locale:y,children:[(0,l.jsx)(n.x$1,{}),O("profile")]}),(0,l.jsxs)(d,{href:"/profile/submitted",locale:y,children:[(0,l.jsx)(n.svy,{}),O("my_submissions")]}),(0,l.jsxs)(d,{href:"/profile/liked",locale:y,children:[(0,l.jsx)(n.Mbv,{}),O("my_favorites")]}),(0,l.jsxs)(d,{href:"/submit",locale:y,children:[(0,l.jsx)(n.OiG,{}),O("submit_tool")]})]}),(null==(v=j.user)?void 0:v.role)==="admin"&&(0,l.jsx)("div",{className:"border-t py-2",children:(0,l.jsxs)(d,{href:"/admin",locale:y,children:[(0,l.jsx)(n.Pcn,{}),O("admin_panel")]})}),(0,l.jsxs)("div",{className:"border-t py-2",children:[(0,l.jsxs)(d,{href:"/settings",locale:y,children:[(0,l.jsx)(n.Pcn,{}),O("settings")]}),(0,l.jsxs)("button",{onClick:C,className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,l.jsx)(n.axc,{}),O("logout")]})]})]})]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>w(!0),children:[(0,l.jsx)(n.Zu,{}),O("login")]}),(0,l.jsx)(c.A,{isOpen:k,onClose:()=>w(!1)})]})}},7652:(e,t,r)=>{"use strict";r.d(t,{c3:()=>a});var l=r(3385);function s(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let a=s(0,l.c3);s(0,l.kc)},9984:(e,t,r)=>{"use strict";function l(e){return e}r.d(t,{A:()=>l})}},e=>{var t=t=>e(e.s=t);e.O(0,[768,6711,3385,6160,2108,8441,1684,7358],()=>t(6847)),_N_E=e.O()}]);