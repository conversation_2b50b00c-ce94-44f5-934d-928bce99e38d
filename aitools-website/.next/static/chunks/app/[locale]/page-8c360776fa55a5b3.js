(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>l,a8:()=>s,rd:()=>c});var n=r(9984),o=r(981);let i=(0,n.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:l,redirect:a,usePathname:s,useRouter:c}=(0,o.A)(i)},4354:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(5155);r(2115);var o=r(2388),i=r(7652);let l=e=>{let{category:t}=e,r=(0,i.c3)("common");return(0,n.jsx)(o.N_,{href:"/categories/".concat(t.slug),children:(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",style:{height:"100%"},children:(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:t.color||"#3B82F6"},children:(0,n.jsx)("span",{className:"text-white",children:t.icon||"\uD83D\uDD27"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:r("tools_count",{count:t.toolCount})})]})]}),(0,n.jsx)("p",{className:"text-gray-600 text-sm",children:t.description})]})})})}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var n=r(2115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),l=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>n.createElement(u,a({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:o,size:i,title:s}=e,d=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,l),u=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),s&&n.createElement("title",null,s),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}},4601:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(5155),o=r(2115),i=r(2388),l=r(7652),a=r(2108),s=r(9911),c=r(6214);function d(e){let{toolId:t,initialLikes:r=0,initialLiked:d=!1,onLoginRequired:u,onUnlike:f,isInLikedPage:m=!1,showCount:h=!0,size:b="md"}=e,{data:v}=(0,a.useSession)(),{getToolState:p,initializeToolState:y,toggleLike:g}=(0,c.X)(),x=(0,i.a8)(),k=(0,l.c3)("common");null==x||x.startsWith("/en");let j=p(t);(0,o.useEffect)(()=>{y(t,r,d)},[t,r,d]);let O=async()=>{if(!v){null==u||u();return}if(j.loading)return;let e=j.liked;await g(t,m)&&m&&e&&f&&f(t)},w=(()=>{switch(b){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,n.jsxs)("button",{onClick:O,disabled:j.loading,className:"\n        ".concat(w.button,"\n        inline-flex items-center space-x-1\n        ").concat(j.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:k(j.liked?"unlike":"like"),children:[j.loading?(0,n.jsx)("div",{className:"".concat(w.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):j.liked?(0,n.jsx)(s.Mbv,{className:w.icon}):(0,n.jsx)(s.sOK,{className:w.icon}),h&&(0,n.jsx)("span",{className:"".concat(w.text," font-medium"),children:j.likes})]})}},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(3385),o=r(5155);function i(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,o.jsx)(n.Dk,{locale:t,...r})}},6214:(e,t,r)=>{"use strict";r.d(t,{LikeProvider:()=>s,X:()=>c});var n=r(5155),o=r(2115),i=r(2108);let l={liked:!1,likes:0,loading:!1},a=(0,o.createContext)(null);function s(e){let{children:t}=e,{data:r}=(0,i.useSession)(),[s,c]=(0,o.useState)({}),d=(0,o.useCallback)(e=>s[e]||l,[s]),u=(0,o.useCallback)(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];c(n=>n[e]?n:{...n,[e]:{liked:r,likes:t,loading:!1}})},[]),f=(0,o.useCallback)(async e=>{if(r)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let r=await t.json();r.success&&c(t=>({...t,[e]:{liked:r.data.liked,likes:r.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[r]),m=(0,o.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!r)return!1;c(t=>({...t,[e]:{...t[e]||l,loading:!0}}));try{let r=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(r.ok){let t=await r.json();if(t.success)return c(r=>({...r,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return c(t=>({...t,[e]:{...t[e]||l,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),c(t=>({...t,[e]:{...t[e]||l,loading:!1}})),!1}},[r]);return(0,o.useEffect)(()=>{r?Object.keys(s).forEach(e=>{f(e)}):c(e=>{let t={};return Object.keys(e).forEach(r=>{t[r]={...e[r],liked:!1,loading:!1}}),t})},[r]),(0,n.jsx)(a.Provider,{value:{toolStates:s,toggleLike:m,getToolState:d,initializeToolState:u,refreshToolState:f},children:t})}function c(){let e=(0,o.useContext)(a);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},7563:(e,t,r)=>{Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.t.bind(r,3063,23)),Promise.resolve().then(r.bind(r,4354)),Promise.resolve().then(r.bind(r,4601))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3385,6160,2108,8003,8441,1684,7358],()=>t(7563)),_N_E=e.O()}]);