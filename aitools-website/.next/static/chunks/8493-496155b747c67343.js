(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8493],{2388:(e,t,s)=>{"use strict";s.d(t,{N_:()=>i,a8:()=>n,rd:()=>c});var l=s(9984),a=s(981);let r=(0,l.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:i,redirect:o,usePathname:n,useRouter:c}=(0,a.A)(r)},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var l=s(5155);function a(e){let{size:t="md",className:s=""}=e;return(0,l.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,l.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>n,S9:()=>m,Y$:()=>o,kX:()=>l,mV:()=>c,mp:()=>g,sT:()=>u,tF:()=>x,v4:()=>i,vS:()=>a});let l={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",promotion:{enabled:!0,description:"限时优惠 - 前100个付费用户",discountPercent:50,remainingSlots:85},features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:l.FREE_LAUNCH.description,price:l.FREE_LAUNCH.displayPrice,features:l.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:l.PRIORITY_LAUNCH.description,price:l.PRIORITY_LAUNCH.displayPrice,features:l.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],o=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),m=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),u=()=>l.PRIORITY_LAUNCH.promotion,g=()=>{let e=l.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},x=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4035:(e,t,s)=>{"use strict";s.d(t,{default:()=>N});var l=s(5155),a=s(2115),r=s(2108),i=s(2388),o=s(7652),n=s(2731),c=s(5734),d=s(6063),m=s(3467),u=s(7550),g=s(9869),x=s(1284),h=s(8164);let b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var p=s(4416),f=s(7924),j=s(3332);function v(e){let{selectedTags:t,onTagsChange:s,maxTags:r=3,placeholder:n}=e,[c,d]=(0,a.useState)(""),[m,u]=(0,a.useState)(!1),g=(0,i.a8)(),x=(0,o.c3)("common"),h=function(){let e=(0,o.c3)("tags");return b.map(t=>({key:t,label:e(t)}))}();null==g||g.startsWith("/en");let v=e=>{t.includes(e)?s(t.filter(t=>t!==e)):t.length<r&&s([...t,e])},y=e=>{s(t.filter(t=>t!==e))},N=h.filter(e=>e.label.toLowerCase().includes(c.toLowerCase())&&!t.includes(e.key)),w=e=>{let t=h.find(t=>t.key===e);return t?t.label:e};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:x("select_tags")}),(0,l.jsx)("span",{className:"text-sm text-gray-500",children:x("selected_count",{count:t.length,max:r})})]}),t.length>0&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:x("selected_tags")}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,l.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[w(e),(0,l.jsx)("button",{type:"button",onClick:()=>y(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,l.jsx)(p.A,{className:"h-3 w-3"})})]},e))})]}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:x("select_tags_max",{max:r})}),(0,l.jsxs)("div",{className:"relative mb-3",children:[(0,l.jsx)("input",{type:"text",placeholder:n||x("search_tags"),value:c,onChange:e=>d(e.target.value),onFocus:()=>u(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,l.jsx)(f.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(m||c)&&(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:N.length>0?(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 gap-1",children:N.map(e=>{let s=t.length>=r;return(0,l.jsx)("button",{type:"button",onClick:()=>{v(e.key),d(""),u(!1)},disabled:s,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(s?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(j.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e.label]})},e.key)})}),N.length>50&&(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:x("found_tags",{count:N.length})})]}):(0,l.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:x(c?"no_tags_found":"start_typing")})})})]})}),(m||c)&&(0,l.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{u(!1),d("")}}),t.length>=r&&(0,l.jsx)("p",{className:"text-sm text-amber-600",children:x("max_tags_limit",{max:r})})]})}var y=s(9651);function N(e){let{categoryOptions:t,isEditMode:s=!1,toolId:b,initialTool:p}=e,f=(0,o.c3)("submit"),{data:j,status:N}=(0,r.useSession)(),w=(0,i.rd)(),[_,C]=(0,a.useState)(p||null),[I,A]=(0,a.useState)(s&&!p),[k,E]=(0,a.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[F,R]=(0,a.useState)(null),[S,P]=(0,a.useState)(""),[T,L]=(0,a.useState)(!1),[U,M]=(0,a.useState)(!1),[D,z]=(0,a.useState)("idle"),[Z,O]=(0,a.useState)(""),[H,q]=(0,a.useState)({}),[W,Y]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(!s||!b||p)return;let e=async()=>{try{let e=await fetch("/api/tools/".concat(b)),t=await e.json();if(t.success){let e=t.data;C(e),E({name:e.name||"",tagline:e.tagline||"",description:e.description||"",website:e.website||"",logoFile:null,category:e.category||"",tags:e.tags||[],pricing:e.pricing||""}),P(e.logo||""),R(e.logo||"")}else z("error"),O(t.message||"获取工具信息失败")}catch(e){console.error("获取工具信息失败:",e),z("error"),O("网络错误，请重试")}finally{A(!1)}};j?e():"loading"!==N&&A(!1)},[b,j,N,s,p]),(0,a.useEffect)(()=>{s&&p&&(C(p),E({name:p.name||"",tagline:p.tagline||"",description:p.description||"",website:p.website||"",logoFile:null,category:p.category||"",tags:p.tags||[],pricing:p.pricing||""}),P(p.logo||""),R(p.logo||""),A(!1))},[s,p]);let B=e=>{let{name:t,value:s}=e.target;E(e=>({...e,[t]:s}))},J=e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];if(s){E(e=>({...e,logoFile:s}));let e=new FileReader;e.onload=e=>{var t;R(null==(t=e.target)?void 0:t.result)},e.readAsDataURL(s)}},G=()=>{let e={};return k.name.trim()||(e.name=f("form.tool_name")+" is required"),k.description.trim()||(e.description=f("form.description")+" is required"),k.website.trim()||(e.website=f("form.website_url")+" is required"),k.category||(e.category=f("form.category")+" is required"),k.pricing||(e.pricing=f("form.pricing_model")+" is required"),k.website&&!k.website.match(/^https?:\/\/.+/)&&(e.website=f("form.website_url_placeholder")),s||k.logoFile||(e.logo=f("form.logo_required")),0===k.tags.length&&(e.tags=f("form.tags_placeholder")),q(e),0===Object.keys(e).length},V=async e=>{var t;if(e.preventDefault(),!(null==j||null==(t=j.user)?void 0:t.email))return void Y(!0);if(G()){M(!0),z("idle");try{let e=S;if(k.logoFile){let t=new FormData;t.append("logo",k.logoFile);let s=await fetch("/api/upload/logo",{method:"POST",body:t});if(s.ok)e=(await s.json()).data.url;else{let e=await s.json();throw Error(e.message||"Logo upload failed")}}if(s&&b){let t={name:k.name,tagline:k.tagline,description:k.description,website:k.website,logo:e||void 0,category:k.category,tags:k.tags,pricing:k.pricing},s=await fetch("/api/tools/".concat(b),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),l=await s.json();l.success?(z("success"),O("工具信息更新成功！"),setTimeout(()=>{w.push("/profile/submitted")},2e3)):(z("error"),O(l.error||"Update failed, please retry"))}else{let t={name:k.name,tagline:k.tagline,description:k.description,website:k.website,logoUrl:e,category:k.category,tags:k.tags,pricing:k.pricing},s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok){let e=await s.json();z("success"),O(f("form.success_message")),setTimeout(()=>{w.push("/submit/tool-info-success?toolId=".concat(e.data.toolId))},2e3)}else{let e=await s.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),z("error"),O(e.message+". "+f("form.error_message"))}finally{M(!1)}}};return"loading"===N||I?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(n.A,{size:"lg"})}):j?s&&!_?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,l.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===D?(0,l.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,l.jsx)(c.A,{message:Z||f("form.success_message")})}):(0,l.jsxs)(a.Fragment,{children:[(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s&&(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)(i.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,l.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,l.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!s&&(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsx)("div",{className:"flex justify-center mb-4",children:(0,l.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,l.jsx)(g.A,{className:"h-8 w-8 text-blue-600"})})}),(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:f("title")}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:f("subtitle")})]}),(0,l.jsxs)("form",{onSubmit:V,className:"max-w-4xl mx-auto space-y-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,l.jsx)(x.A,{className:"h-5 w-5 mr-2 text-blue-600"}),f("form.basic_info")]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tool_name")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)("input",{type:"text",id:"name",name:"name",value:k.name,onChange:B,placeholder:f("form.tool_name_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tagline")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:k.tagline,onChange:B,placeholder:f("form.tagline_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.description")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)("textarea",{id:"description",name:"description",value:k.description,onChange:B,placeholder:f("form.description_placeholder"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.website_url")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,l.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),!s&&(0,l.jsx)("input",{type:"url",id:"website",name:"website",value:k.website,onChange:B,placeholder:s?"https://example.com":f("form.website_url_placeholder"),className:"w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(H.website?"border-red-300":"border-gray-300"),required:!0}),s&&(0,l.jsx)("div",{className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:k.website})]}),H.website&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:H.website})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.logo_upload")," ",!s&&(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,l.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;(null==(t=e.target.files)?void 0:t[0])&&J(e)},className:"hidden",id:"logo-upload",required:!s}),(0,l.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,l.jsx)(g.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:T?f("form.uploading"):f("form.click_to_upload")}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:f("form.logo_upload_hint")})]})]}),H.logo&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:H.logo})]}),F&&(0,l.jsx)(y.Ay,{alt:"app logo",src:F,width:y.iu.toolLogo.width,height:y.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:y.ng.toolLogo,placeholder:"blur"})]})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:f("form.category_and_pricing")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.category")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("select",{id:"category",name:"category",value:k.category,onChange:B,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,l.jsx)("option",{value:"",children:f("form.category_placeholder")}),t.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.pricing_model")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("select",{id:"pricing",name:"pricing",value:k.pricing,onChange:B,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(H.pricing?"border-red-300":"border-gray-300"),required:!0,children:[(0,l.jsx)("option",{value:"",children:f("form.pricing_placeholder")}),m.Y$.map(e=>(0,l.jsx)("option",{value:e.value,children:f("form.".concat(e.value))},e.value))]}),H.pricing&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:H.pricing})]})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tags")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)(v,{selectedTags:k.tags,onTagsChange:e=>{E(t=>({...t,tags:e}))},maxTags:3,placeholder:f("form.tags_placeholder")}),H.tags&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:H.tags})]})]}),!s&&(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:f("form.guidelines_title")}),(0,l.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_1")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_2")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_3")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_4")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_5")]})]})]}),(0,l.jsx)("div",{className:s?"flex justify-end":"flex justify-center",children:(0,l.jsx)("button",{type:"submit",disabled:U,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ".concat("px-8 py-3 text-base"),children:U?(0,l.jsxs)(a.Fragment,{children:[(0,l.jsx)(n.A,{size:"sm",className:"mr-2"}),f("form.submitting")]}):(0,l.jsxs)(a.Fragment,{children:[(0,l.jsx)(g.A,{className:"h-5 w-5 mr-2"}),f("form.submit_button")]})})})]}),"error"===D&&(0,l.jsx)("div",{className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,l.jsx)("p",{className:"text-red-800",children:Z})})]}),(0,l.jsx)(d.A,{isOpen:W,onClose:()=>Y(!1)})]}):(0,l.jsxs)(a.Fragment,{children:[(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:f("auth.login_required")}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:f("auth.login_to_submit")}),(0,l.jsx)("button",{onClick:()=>Y(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:f("auth.login")})]})}),(0,l.jsx)(d.A,{isOpen:W,onClose:()=>Y(!1)})]})}},5734:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var l=s(5155),a=s(646),r=s(4416);function i(e){let{message:t,onClose:s,className:i=""}=e;return(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsx)("p",{className:"text-green-800 text-sm",children:t})}),s&&(0,l.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,l.jsx)(r.A,{className:"w-4 h-4"})})]})})}},6063:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var l=s(5155),a=s(2115),r=s(2108),i=s(2388),o=s(7652),n=s(3385),c=s(9911);function d(e){let{isOpen:t,onClose:s}=e,[d,m]=(0,a.useState)("method"),[u,g]=(0,a.useState)(""),[x,h]=(0,a.useState)(""),[b,p]=(0,a.useState)(!1),[f,j]=(0,a.useState)("");(0,i.a8)();let v=(0,o.c3)("auth");(0,n.Ym)();let y=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",s=document.createElement("div");s.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},N=()=>{m("method"),g(""),h(""),j(""),s()},w=async e=>{try{p(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){y(v("login_failed"),"error")}finally{p(!1)}},_=async()=>{if(!u)return void j(v("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u))return void j(v("email_invalid"));j(""),p(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:u})}),t=await e.json();t.success?(h(t.token),m("code"),y(v("verification_sent"))):y(t.error||v("send_failed"),"error")}catch(e){y(v("network_error"),"error")}finally{p(!1)}},C=async e=>{if(6===e.length){p(!0);try{let t=await (0,r.signIn)("email-code",{email:u,code:e,token:x,redirect:!1});(null==t?void 0:t.ok)?(y(v("login_success")),N()):y((null==t?void 0:t.error)||v("verification_error"),"error")}catch(e){y(v("network_error"),"error")}finally{p(!1)}}},I=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");if(s[e].value=t,t&&e<5){var l;null==(l=s[e+1])||l.focus()}let a=Array.from(s).map(e=>e.value).join("");6===a.length&&C(a)};return t?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===d&&v("login_title"),"email"===d&&v("email_login_title"),"code"===d&&v("verification_title")]}),(0,l.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(c.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:v("choose_method")}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>w("google"),disabled:b,children:[(0,l.jsx)(c.DSS,{}),v("google_login")]})}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:v("or")})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>m("email"),children:[(0,l.jsx)(c.maD,{}),v("email_login")]})]}),"email"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:v("email_instruction")}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("email_address")}),(0,l.jsx)("input",{type:"email",value:u,onChange:e=>g(e.target.value),placeholder:v("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&_(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),f&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:_,disabled:b,children:b?v("sending"):v("send_code")}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:v("back")})]})]}),"code"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:v("verification_instruction",{email:u})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("verification_code")}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:t=>I(e,t.target.value),disabled:b,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("email"),children:v("resend_code")}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:v("back")})]})]})]})]})]}):null}},8493:(e,t,s)=>{Promise.resolve().then(s.bind(s,6096)),Promise.resolve().then(s.bind(s,4035))},9651:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>r,iu:()=>i,ng:()=>o});var l=s(5155);s(2115);var a=s(6766);function r(e){let{src:t,alt:s,width:r,height:i,className:o="",priority:n=!1,fill:c=!1,sizes:d,placeholder:m="empty",blurDataURL:u,fallbackSrc:g="/images/placeholder.svg"}=e,x={src:t,alt:s,className:o,priority:n,placeholder:"blur"===m?"blur":"empty",blurDataURL:u||("blur"===m?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==":void 0),sizes:d||(c?"100vw":void 0)};return c?(0,l.jsx)("div",{className:"relative overflow-hidden",children:(0,l.jsx)(a.default,{...x,fill:!0,style:{objectFit:"contain",padding:2}})}):(0,l.jsx)(a.default,{...x,width:r,height:i})}let i={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:128,height:128},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},o={avatar:"40px",toolLogo:"52px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}}}]);