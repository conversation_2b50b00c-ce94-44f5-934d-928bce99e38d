{"/_not-found/page": "/_not-found", "/api/admin/stats/route": "/api/admin/stats", "/api/admin/tools/[id]/reject/route": "/api/admin/tools/[id]/reject", "/api/admin/tools/[id]/approve/route": "/api/admin/tools/[id]/approve", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/me/route": "/api/auth/me", "/api/admin/tools/route": "/api/admin/tools", "/api/auth/send-code/route": "/api/auth/send-code", "/api/categories/route": "/api/categories", "/api/orders/[id]/pay/route": "/api/orders/[id]/pay", "/api/stripe/create-payment-intent/route": "/api/stripe/create-payment-intent", "/api/orders/[id]/route": "/api/orders/[id]", "/api/test/create-payment-intent/route": "/api/test/create-payment-intent", "/api/stripe/webhook/route": "/api/stripe/webhook", "/api/tools/[id]/comments/route": "/api/tools/[id]/comments", "/api/tools/[id]/launch-date/route": "/api/tools/[id]/launch-date", "/api/tools/[id]/like/route": "/api/tools/[id]/like", "/api/tools/[id]/route": "/api/tools/[id]", "/api/tools/publish/route": "/api/tools/publish", "/api/tools/route": "/api/tools", "/api/upload/avatar/route": "/api/upload/avatar", "/api/tools/submit/route": "/api/tools/submit", "/api/upload/logo/route": "/api/upload/logo", "/api/user/tools/route": "/api/user/tools", "/favicon.ico/route": "/favicon.ico", "/api/user/liked-tools/route": "/api/user/liked-tools", "/sitemap.xml/route": "/sitemap.xml", "/page": "/", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/admin/dashboard/page": "/[locale]/admin/dashboard", "/[locale]/admin/tools/[id]/page": "/[locale]/admin/tools/[id]", "/[locale]/categories/[slug]/page": "/[locale]/categories/[slug]", "/[locale]/demo-features/page": "/[locale]/demo-features", "/[locale]/payment/checkout/page": "/[locale]/payment/checkout", "/[locale]/profile/liked/page": "/[locale]/profile/liked", "/[locale]/dashboard/page": "/[locale]/dashboard", "/[locale]/search/page": "/[locale]/search", "/[locale]/profile/submitted/page": "/[locale]/profile/submitted", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/submit/launch-date/[toolId]/page": "/[locale]/submit/launch-date/[toolId]", "/[locale]/submit/tool-info-success/page": "/[locale]/submit/tool-info-success", "/[locale]/submit/edit/[toolId]/page": "/[locale]/submit/edit/[toolId]", "/[locale]/submit/launch-date-success/page": "/[locale]/submit/launch-date-success", "/[locale]/submit/success/page": "/[locale]/submit/success", "/[locale]/test-auth/page": "/[locale]/test-auth", "/[locale]/submit/page": "/[locale]/submit", "/[locale]/test-promotion/page": "/[locale]/test-promotion", "/[locale]/test-pricing/page": "/[locale]/test-pricing", "/[locale]/categories/page": "/[locale]/categories", "/[locale]/about/page": "/[locale]/about", "/[locale]/contact/page": "/[locale]/contact", "/[locale]/page": "/[locale]", "/[locale]/privacy/page": "/[locale]/privacy", "/[locale]/tools/page": "/[locale]/tools", "/[locale]/terms/page": "/[locale]/terms", "/[locale]/tools/[id]/page": "/[locale]/tools/[id]", "/[locale]/test-stripe/page": "/[locale]/test-stripe"}